import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { tenantApi } from '../../services/api/tenantApi';

export interface Tenant {
  id: string;
  name: string;
  code: string;
  description?: string;
  contactEmail: string;
  contactPhone?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  isActive: boolean;
  subscriptionPlan?: string;
  subscriptionStartDate?: string;
  subscriptionEndDate?: string;
  maxStudents: number;
  maxStaff: number;
  currentStudents: number;
  currentStaff: number;
  settings?: Record<string, any>;
  brandingConfig?: Record<string, any>;
  complianceConfig?: Record<string, any>;
  createdAt: string;
  updatedAt?: string;
}

export interface TenantBranding {
  logoUrl?: string;
  primaryColor?: string;
  secondaryColor?: string;
  accentColor?: string;
  fontFamily?: string;
  theme?: string;
  customCss?: Record<string, string>;
  customLabels?: Record<string, string>;
}

export interface TenantState {
  currentTenant: Tenant | null;
  availableTenants: Tenant[];
  branding: TenantBranding | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: number | null;
}

const initialState: TenantState = {
  currentTenant: null,
  availableTenants: [],
  branding: null,
  isLoading: false,
  error: null,
  lastUpdated: null,
};

// Async thunks
export const fetchTenantByCode = createAsyncThunk(
  'tenant/fetchByCode',
  async (code: string, { rejectWithValue }) => {
    try {
      const response = await tenantApi.getTenantByCode(code);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch tenant');
    }
  }
);

export const fetchTenantById = createAsyncThunk(
  'tenant/fetchById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await tenantApi.getTenantById(id);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch tenant');
    }
  }
);

export const fetchAvailableTenants = createAsyncThunk(
  'tenant/fetchAvailable',
  async (_, { rejectWithValue }) => {
    try {
      const response = await tenantApi.getAllTenants();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch tenants');
    }
  }
);

export const updateTenantConfiguration = createAsyncThunk(
  'tenant/updateConfiguration',
  async (
    { tenantId, configuration }: { tenantId: string; configuration: Partial<Tenant> },
    { rejectWithValue }
  ) => {
    try {
      const response = await tenantApi.updateTenantConfiguration(tenantId, configuration);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update configuration');
    }
  }
);

export const updateTenantBranding = createAsyncThunk(
  'tenant/updateBranding',
  async (
    { tenantId, branding }: { tenantId: string; branding: TenantBranding },
    { rejectWithValue }
  ) => {
    try {
      const response = await tenantApi.updateTenantBranding(tenantId, branding);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update branding');
    }
  }
);

export const checkTenantStatus = createAsyncThunk(
  'tenant/checkStatus',
  async (tenantId: string, { rejectWithValue }) => {
    try {
      const response = await tenantApi.getTenantStatus(tenantId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to check tenant status');
    }
  }
);

const tenantSlice = createSlice({
  name: 'tenant',
  initialState,
  reducers: {
    setCurrentTenant: (state, action: PayloadAction<Tenant>) => {
      state.currentTenant = action.payload;
      state.branding = action.payload.brandingConfig || null;
      state.lastUpdated = Date.now();
      
      // Store tenant info in localStorage for persistence
      localStorage.setItem('currentTenant', JSON.stringify(action.payload));
    },
    clearCurrentTenant: (state) => {
      state.currentTenant = null;
      state.branding = null;
      state.lastUpdated = null;
      
      // Clear from localStorage
      localStorage.removeItem('currentTenant');
    },
    setBranding: (state, action: PayloadAction<TenantBranding>) => {
      state.branding = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    loadTenantFromStorage: (state) => {
      const storedTenant = localStorage.getItem('currentTenant');
      if (storedTenant) {
        try {
          const tenant = JSON.parse(storedTenant);
          state.currentTenant = tenant;
          state.branding = tenant.brandingConfig || null;
          state.lastUpdated = Date.now();
        } catch (error) {
          console.error('Failed to parse stored tenant:', error);
          localStorage.removeItem('currentTenant');
        }
      }
    },
    updateTenantSettings: (state, action: PayloadAction<Record<string, any>>) => {
      if (state.currentTenant) {
        state.currentTenant.settings = {
          ...state.currentTenant.settings,
          ...action.payload,
        };
        
        // Update localStorage
        localStorage.setItem('currentTenant', JSON.stringify(state.currentTenant));
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Tenant by Code
      .addCase(fetchTenantByCode.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTenantByCode.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentTenant = action.payload;
        state.branding = action.payload.brandingConfig || null;
        state.lastUpdated = Date.now();
        
        // Store in localStorage
        localStorage.setItem('currentTenant', JSON.stringify(action.payload));
      })
      .addCase(fetchTenantByCode.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch Tenant by ID
      .addCase(fetchTenantById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTenantById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentTenant = action.payload;
        state.branding = action.payload.brandingConfig || null;
        state.lastUpdated = Date.now();
        
        // Store in localStorage
        localStorage.setItem('currentTenant', JSON.stringify(action.payload));
      })
      .addCase(fetchTenantById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch Available Tenants
      .addCase(fetchAvailableTenants.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAvailableTenants.fulfilled, (state, action) => {
        state.isLoading = false;
        state.availableTenants = action.payload;
      })
      .addCase(fetchAvailableTenants.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Update Configuration
      .addCase(updateTenantConfiguration.fulfilled, (state, action) => {
        if (state.currentTenant && state.currentTenant.id === action.payload.tenantId) {
          state.currentTenant = { ...state.currentTenant, ...action.payload };
          state.lastUpdated = Date.now();
          
          // Update localStorage
          localStorage.setItem('currentTenant', JSON.stringify(state.currentTenant));
        }
      })
      .addCase(updateTenantConfiguration.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      
      // Update Branding
      .addCase(updateTenantBranding.fulfilled, (state, action) => {
        state.branding = action.payload;
        if (state.currentTenant) {
          state.currentTenant.brandingConfig = action.payload;
          state.lastUpdated = Date.now();
          
          // Update localStorage
          localStorage.setItem('currentTenant', JSON.stringify(state.currentTenant));
        }
      })
      .addCase(updateTenantBranding.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      
      // Check Status
      .addCase(checkTenantStatus.fulfilled, (state, action) => {
        if (state.currentTenant && state.currentTenant.id === action.payload.tenantId) {
          state.currentTenant.isActive = action.payload.isActive;
          state.lastUpdated = Date.now();
          
          // Update localStorage
          localStorage.setItem('currentTenant', JSON.stringify(state.currentTenant));
        }
      });
  },
});

export const {
  setCurrentTenant,
  clearCurrentTenant,
  setBranding,
  clearError,
  loadTenantFromStorage,
  updateTenantSettings,
} = tenantSlice.actions;

export default tenantSlice.reducer;
