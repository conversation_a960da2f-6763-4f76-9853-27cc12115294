# Use the official .NET 8 SDK image for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy solution file
COPY SchoolManagement.sln .

# Copy project files
COPY src/backend/SchoolManagement.Core/SchoolManagement.Core.csproj src/backend/SchoolManagement.Core/
COPY src/backend/SchoolManagement.Infrastructure/SchoolManagement.Infrastructure.csproj src/backend/SchoolManagement.Infrastructure/
COPY src/backend/SchoolManagement.Application/SchoolManagement.Application.csproj src/backend/SchoolManagement.Application/
COPY src/backend/SchoolManagement.Api/SchoolManagement.Api.csproj src/backend/SchoolManagement.Api/

# Restore dependencies
RUN dotnet restore src/backend/SchoolManagement.Api/SchoolManagement.Api.csproj

# Copy source code
COPY src/backend/ src/backend/

# Build the application
WORKDIR /src/src/backend/SchoolManagement.Api
RUN dotnet build -c Release -o /app/build

# Publish the application
RUN dotnet publish -c Release -o /app/publish

# Use the official .NET 8 runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime
WORKDIR /app

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Copy published application
COPY --from=build /app/publish .

# Create directories for logs and uploads
RUN mkdir -p /app/logs /app/uploads

# Set permissions
RUN chown -R app:app /app
USER app

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:5000/health || exit 1

# Entry point
ENTRYPOINT ["dotnet", "SchoolManagement.Api.dll"]
