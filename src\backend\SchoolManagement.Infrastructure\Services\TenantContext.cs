using SchoolManagement.Core.Interfaces;

namespace SchoolManagement.Infrastructure.Services;

/// <summary>
/// Implementation of tenant context for multi-tenant support
/// </summary>
public class TenantContext : ITenantContext
{
    private Guid? _tenantId;
    private TenantInfo? _tenantInfo;

    public Guid? TenantId => _tenantId;

    public TenantInfo? Tenant => _tenantInfo;

    public bool HasTenant => _tenantId.HasValue;

    public void SetTenant(Guid tenantId, TenantInfo? tenantInfo = null)
    {
        _tenantId = tenantId;
        _tenantInfo = tenantInfo;
    }

    public void ClearTenant()
    {
        _tenantId = null;
        _tenantInfo = null;
    }
}

/// <summary>
/// Scoped tenant context that maintains tenant information per request
/// </summary>
public class ScopedTenantContext : ITenantContext
{
    private readonly AsyncLocal<TenantContextData> _tenantData = new();

    public Guid? TenantId => _tenantData.Value?.TenantId;

    public TenantInfo? Tenant => _tenantData.Value?.TenantInfo;

    public bool HasTenant => _tenantData.Value?.TenantId.HasValue ?? false;

    public void SetTenant(Guid tenantId, TenantInfo? tenantInfo = null)
    {
        _tenantData.Value = new TenantContextData
        {
            TenantId = tenantId,
            TenantInfo = tenantInfo
        };
    }

    public void ClearTenant()
    {
        _tenantData.Value = null;
    }

    private class TenantContextData
    {
        public Guid? TenantId { get; set; }
        public TenantInfo? TenantInfo { get; set; }
    }
}
