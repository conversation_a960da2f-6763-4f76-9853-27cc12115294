import React from 'react';
import {
  Breadcrumbs as MuiBreadcrumbs,
  <PERSON>,
  Typography,
  Box,
  Chip,
} from '@mui/material';
import {
  NavigateNext as NavigateNextIcon,
  Home as HomeIcon,
} from '@mui/icons-material';
import { useLocation, Link as RouterLink } from 'react-router-dom';
import { useAppSelector } from '../../store/store';

interface BreadcrumbItem {
  label: string;
  path?: string;
  icon?: React.ReactNode;
}

const Breadcrumbs: React.FC = () => {
  const location = useLocation();
  const { pageTitle } = useAppSelector((state) => state.ui);
  const { currentTenant } = useAppSelector((state) => state.tenant);

  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathnames = location.pathname.split('/').filter((x) => x);
    const breadcrumbs: BreadcrumbItem[] = [
      {
        label: 'Dashboard',
        path: '/dashboard',
        icon: <HomeIcon sx={{ fontSize: 16 }} />,
      },
    ];

    // Map path segments to readable labels
    const pathLabels: Record<string, string> = {
      students: 'Students',
      staff: 'Staff',
      academics: 'Academics',
      finance: 'Finance',
      attendance: 'Attendance',
      reports: 'Reports',
      settings: 'Settings',
      enrollment: 'Enrollment',
      guardians: 'Guardians',
      teachers: 'Teachers',
      payroll: 'Payroll',
      grades: 'Grades & Classes',
      subjects: 'Subjects',
      schedule: 'Schedule',
      exams: 'Exams',
      fees: 'Fee Management',
      payments: 'Payments',
      daily: 'Daily Attendance',
      calendar: 'Calendar View',
      academic: 'Academic Reports',
      financial: 'Financial Reports',
      general: 'General Settings',
      users: 'User Management',
      system: 'System Settings',
    };

    let currentPath = '';
    pathnames.forEach((pathname, index) => {
      currentPath += `/${pathname}`;
      
      // Skip if this is the dashboard path
      if (currentPath === '/dashboard') return;
      
      const label = pathLabels[pathname] || pathname.charAt(0).toUpperCase() + pathname.slice(1);
      const isLast = index === pathnames.length - 1;
      
      breadcrumbs.push({
        label,
        path: isLast ? undefined : currentPath,
      });
    });

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', py: 1 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <MuiBreadcrumbs
          separator={<NavigateNextIcon fontSize="small" />}
          aria-label="breadcrumb"
          sx={{
            '& .MuiBreadcrumbs-separator': {
              color: 'text.secondary',
            },
          }}
        >
          {breadcrumbs.map((breadcrumb, index) => {
            const isLast = index === breadcrumbs.length - 1;
            
            if (isLast || !breadcrumb.path) {
              return (
                <Typography
                  key={breadcrumb.label}
                  color="text.primary"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.5,
                    fontWeight: isLast ? 600 : 400,
                  }}
                >
                  {breadcrumb.icon}
                  {breadcrumb.label}
                </Typography>
              );
            }

            return (
              <Link
                key={breadcrumb.label}
                component={RouterLink}
                to={breadcrumb.path}
                underline="hover"
                color="inherit"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                  '&:hover': {
                    color: 'primary.main',
                  },
                }}
              >
                {breadcrumb.icon}
                {breadcrumb.label}
              </Link>
            );
          })}
        </MuiBreadcrumbs>

        {pageTitle && (
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: 'text.primary',
              ml: 1,
            }}
          >
            {pageTitle}
          </Typography>
        )}
      </Box>

      {/* Tenant indicator */}
      {currentTenant && (
        <Chip
          label={currentTenant.name}
          size="small"
          variant="outlined"
          sx={{
            borderColor: 'primary.main',
            color: 'primary.main',
            fontWeight: 500,
          }}
        />
      )}
    </Box>
  );
};

export default Breadcrumbs;
