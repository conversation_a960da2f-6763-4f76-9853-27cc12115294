import React from 'react';
import { Box, CircularProgress, Typography, LinearProgress } from '@mui/material';
import { styled } from '@mui/material/styles';

const LoadingContainer = styled(Box)(({ theme }) => ({
  position: 'fixed',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'center',
  backgroundColor: theme.palette.background.default,
  zIndex: 9999,
}));

const LogoContainer = styled(Box)(({ theme }) => ({
  width: 80,
  height: 80,
  borderRadius: 16,
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(3),
  color: 'white',
  fontSize: '2rem',
  fontWeight: 700,
}));

interface LoadingScreenProps {
  message?: string;
  showProgress?: boolean;
  progress?: number;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({ 
  message = 'Loading...', 
  showProgress = false,
  progress 
}) => {
  return (
    <LoadingContainer>
      <LogoContainer>
        SMS
      </LogoContainer>
      
      <Typography variant="h5" gutterBottom sx={{ fontWeight: 600, mb: 1 }}>
        School Management System
      </Typography>
      
      <Typography variant="body2" color="text.secondary" sx={{ mb: 4 }}>
        {message}
      </Typography>
      
      {showProgress && progress !== undefined ? (
        <Box sx={{ width: 300, mb: 2 }}>
          <LinearProgress 
            variant="determinate" 
            value={progress} 
            sx={{ 
              height: 6, 
              borderRadius: 3,
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
              '& .MuiLinearProgress-bar': {
                borderRadius: 3,
              }
            }} 
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block', textAlign: 'center' }}>
            {Math.round(progress)}%
          </Typography>
        </Box>
      ) : (
        <CircularProgress 
          size={40} 
          thickness={4}
          sx={{ 
            color: 'primary.main',
            '& .MuiCircularProgress-circle': {
              strokeLinecap: 'round',
            }
          }} 
        />
      )}
    </LoadingContainer>
  );
};

export default LoadingScreen;
