using SchoolManagement.Core.Interfaces;
using System.Security.Claims;

namespace SchoolManagement.Api.Middleware;

/// <summary>
/// Middleware to resolve tenant context from request headers or claims
/// </summary>
public class TenantResolutionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<TenantResolutionMiddleware> _logger;

    public TenantResolutionMiddleware(RequestDelegate next, ILogger<TenantResolutionMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ITenantContext tenantContext, ITenantService tenantService)
    {
        try
        {
            var tenantId = await ResolveTenantIdAsync(context, tenantService);
            
            if (tenantId.HasValue)
            {
                var tenantInfo = await tenantService.GetTenantInfoAsync(tenantId.Value);
                if (tenantInfo != null)
                {
                    // Check if tenant is active
                    var isActive = await tenantService.IsTenantActiveAsync(tenantId.Value);
                    if (!isActive)
                    {
                        context.Response.StatusCode = 403;
                        await context.Response.WriteAsync("Tenant is not active or subscription has expired");
                        return;
                    }

                    tenantContext.SetTenant(tenantId.Value, tenantInfo);
                    _logger.LogDebug("Tenant context set: {TenantId} - {TenantName}", tenantId.Value, tenantInfo.Name);
                }
                else
                {
                    _logger.LogWarning("Tenant not found: {TenantId}", tenantId.Value);
                    context.Response.StatusCode = 404;
                    await context.Response.WriteAsync("Tenant not found");
                    return;
                }
            }
            else
            {
                // For endpoints that don't require tenant context (like tenant creation, health checks)
                var path = context.Request.Path.Value?.ToLower();
                var allowedPaths = new[]
                {
                    "/health",
                    "/swagger",
                    "/api/tenants",
                    "/api/auth/login",
                    "/api/auth/register"
                };

                if (!allowedPaths.Any(p => path?.StartsWith(p) == true))
                {
                    _logger.LogWarning("No tenant context found for path: {Path}", path);
                    context.Response.StatusCode = 400;
                    await context.Response.WriteAsync("Tenant context is required");
                    return;
                }
            }

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in tenant resolution middleware");
            context.Response.StatusCode = 500;
            await context.Response.WriteAsync("Internal server error");
        }
    }

    private async Task<Guid?> ResolveTenantIdAsync(HttpContext context, ITenantService tenantService)
    {
        // Method 1: Check for tenant ID in header
        if (context.Request.Headers.TryGetValue("X-Tenant-Id", out var tenantIdHeader))
        {
            if (Guid.TryParse(tenantIdHeader.FirstOrDefault(), out var tenantId))
            {
                return tenantId;
            }
        }

        // Method 2: Check for tenant code in header
        if (context.Request.Headers.TryGetValue("X-Tenant-Code", out var tenantCodeHeader))
        {
            var tenantCode = tenantCodeHeader.FirstOrDefault();
            if (!string.IsNullOrEmpty(tenantCode))
            {
                var tenant = await tenantService.GetTenantByCodeAsync(tenantCode);
                return tenant?.Id;
            }
        }

        // Method 3: Check for tenant in subdomain
        var host = context.Request.Host.Host;
        if (host.Contains('.'))
        {
            var subdomain = host.Split('.')[0];
            if (!string.IsNullOrEmpty(subdomain) && subdomain != "www" && subdomain != "api")
            {
                var tenant = await tenantService.GetTenantByCodeAsync(subdomain);
                return tenant?.Id;
            }
        }

        // Method 4: Check for tenant in JWT claims
        if (context.User.Identity?.IsAuthenticated == true)
        {
            var tenantClaim = context.User.FindFirst("tenant_id");
            if (tenantClaim != null && Guid.TryParse(tenantClaim.Value, out var claimTenantId))
            {
                return claimTenantId;
            }
        }

        // Method 5: Check for tenant in query parameters (for development/testing)
        if (context.Request.Query.TryGetValue("tenant", out var tenantQuery))
        {
            var tenantValue = tenantQuery.FirstOrDefault();
            if (!string.IsNullOrEmpty(tenantValue))
            {
                if (Guid.TryParse(tenantValue, out var queryTenantId))
                {
                    return queryTenantId;
                }
                else
                {
                    var tenant = await tenantService.GetTenantByCodeAsync(tenantValue);
                    return tenant?.Id;
                }
            }
        }

        return null;
    }
}

/// <summary>
/// Global exception handling middleware
/// </summary>
public class ExceptionHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExceptionHandlingMiddleware> _logger;

    public ExceptionHandlingMiddleware(RequestDelegate next, ILogger<ExceptionHandlingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred");
            await HandleExceptionAsync(context, ex);
        }
    }

    private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";

        var response = new
        {
            error = new
            {
                message = "An error occurred while processing your request",
                details = exception.Message,
                type = exception.GetType().Name,
                timestamp = DateTime.UtcNow
            }
        };

        context.Response.StatusCode = exception switch
        {
            ArgumentException => 400,
            UnauthorizedAccessException => 401,
            InvalidOperationException => 400,
            KeyNotFoundException => 404,
            NotImplementedException => 501,
            _ => 500
        };

        await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
    }
}
