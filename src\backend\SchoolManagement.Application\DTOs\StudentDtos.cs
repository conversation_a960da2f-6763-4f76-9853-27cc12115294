using System.ComponentModel.DataAnnotations;
using SchoolManagement.Core.Entities;

namespace SchoolManagement.Application.DTOs;

/// <summary>
/// DTO for creating a new student
/// </summary>
public class CreateStudentDto
{
    [Required]
    [MaxLength(50)]
    public string StudentNumber { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string LastName { get; set; } = string.Empty;

    [MaxLength(100)]
    public string? MiddleName { get; set; }

    [MaxLength(100)]
    public string? ArabicFirstName { get; set; }

    [MaxLength(100)]
    public string? ArabicLastName { get; set; }

    [MaxLength(100)]
    public string? ArabicMiddleName { get; set; }

    [Required]
    public DateTime DateOfBirth { get; set; }

    [Required]
    [MaxLength(10)]
    public string Gender { get; set; } = string.Empty;

    [MaxLength(50)]
    public string? Nationality { get; set; }

    [MaxLength(50)]
    public string? NationalId { get; set; }

    [MaxLength(50)]
    public string? PassportNumber { get; set; }

    [MaxLength(100)]
    public string? PlaceOfBirth { get; set; }

    [MaxLength(50)]
    public string? BloodType { get; set; }

    [Phone]
    [MaxLength(20)]
    public string? EmergencyContactPhone { get; set; }

    [MaxLength(100)]
    public string? EmergencyContactName { get; set; }

    [MaxLength(100)]
    public string? EmergencyContactRelation { get; set; }

    [MaxLength(500)]
    public string? Address { get; set; }

    [MaxLength(100)]
    public string? City { get; set; }

    [MaxLength(100)]
    public string? State { get; set; }

    [MaxLength(20)]
    public string? PostalCode { get; set; }

    [MaxLength(100)]
    public string? Country { get; set; }

    public DateTime EnrollmentDate { get; set; } = DateTime.UtcNow;

    [MaxLength(500)]
    public string? Notes { get; set; }

    public string? MedicalInfo { get; set; }

    public string? SpecialNeeds { get; set; }

    public string? TransportationInfo { get; set; }

    // User account information
    [Required]
    [EmailAddress]
    [MaxLength(256)]
    public string Email { get; set; } = string.Empty;

    [Phone]
    [MaxLength(20)]
    public string? PhoneNumber { get; set; }

    // Guardian information
    public List<CreateGuardianDto>? Guardians { get; set; }
}

/// <summary>
/// DTO for updating a student
/// </summary>
public class UpdateStudentDto
{
    [Required]
    public Guid Id { get; set; }

    [Required]
    [MaxLength(100)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string LastName { get; set; } = string.Empty;

    [MaxLength(100)]
    public string? MiddleName { get; set; }

    [MaxLength(100)]
    public string? ArabicFirstName { get; set; }

    [MaxLength(100)]
    public string? ArabicLastName { get; set; }

    [MaxLength(100)]
    public string? ArabicMiddleName { get; set; }

    [Required]
    public DateTime DateOfBirth { get; set; }

    [Required]
    [MaxLength(10)]
    public string Gender { get; set; } = string.Empty;

    [MaxLength(50)]
    public string? Nationality { get; set; }

    [MaxLength(50)]
    public string? NationalId { get; set; }

    [MaxLength(50)]
    public string? PassportNumber { get; set; }

    [MaxLength(100)]
    public string? PlaceOfBirth { get; set; }

    [MaxLength(50)]
    public string? BloodType { get; set; }

    [Phone]
    [MaxLength(20)]
    public string? EmergencyContactPhone { get; set; }

    [MaxLength(100)]
    public string? EmergencyContactName { get; set; }

    [MaxLength(100)]
    public string? EmergencyContactRelation { get; set; }

    [MaxLength(500)]
    public string? Address { get; set; }

    [MaxLength(100)]
    public string? City { get; set; }

    [MaxLength(100)]
    public string? State { get; set; }

    [MaxLength(20)]
    public string? PostalCode { get; set; }

    [MaxLength(100)]
    public string? Country { get; set; }

    public StudentStatus Status { get; set; }

    [MaxLength(500)]
    public string? Notes { get; set; }

    public string? MedicalInfo { get; set; }

    public string? SpecialNeeds { get; set; }

    public string? TransportationInfo { get; set; }

    // User account information
    [Required]
    [EmailAddress]
    [MaxLength(256)]
    public string Email { get; set; } = string.Empty;

    [Phone]
    [MaxLength(20)]
    public string? PhoneNumber { get; set; }
}

/// <summary>
/// DTO for student response
/// </summary>
public class StudentDto
{
    public Guid Id { get; set; }
    public string StudentNumber { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string? MiddleName { get; set; }
    public string FullName { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string? ArabicFirstName { get; set; }
    public string? ArabicLastName { get; set; }
    public string? ArabicMiddleName { get; set; }
    public string? ArabicFullName { get; set; }
    public DateTime DateOfBirth { get; set; }
    public int Age { get; set; }
    public string Gender { get; set; } = string.Empty;
    public string? Nationality { get; set; }
    public string? NationalId { get; set; }
    public string? PassportNumber { get; set; }
    public string? PlaceOfBirth { get; set; }
    public string? BloodType { get; set; }
    public string? EmergencyContactPhone { get; set; }
    public string? EmergencyContactName { get; set; }
    public string? EmergencyContactRelation { get; set; }
    public string? Address { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? PostalCode { get; set; }
    public string? Country { get; set; }
    public DateTime EnrollmentDate { get; set; }
    public DateTime? GraduationDate { get; set; }
    public StudentStatus Status { get; set; }
    public string? Notes { get; set; }
    public string? ProfilePictureUrl { get; set; }
    public object? MedicalInfo { get; set; }
    public string? SpecialNeeds { get; set; }
    public string? TransportationInfo { get; set; }
    public string Email { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public List<GuardianDto>? Guardians { get; set; }
    public List<StudentEnrollmentDto>? Enrollments { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// DTO for student summary (for listing)
/// </summary>
public class StudentSummaryDto
{
    public Guid Id { get; set; }
    public string StudentNumber { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string? ArabicFullName { get; set; }
    public DateTime DateOfBirth { get; set; }
    public int Age { get; set; }
    public string Gender { get; set; } = string.Empty;
    public string? CurrentGrade { get; set; }
    public StudentStatus Status { get; set; }
    public string? ProfilePictureUrl { get; set; }
    public DateTime EnrollmentDate { get; set; }
}

/// <summary>
/// DTO for creating a guardian
/// </summary>
public class CreateGuardianDto
{
    [Required]
    [MaxLength(100)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string LastName { get; set; } = string.Empty;

    [MaxLength(100)]
    public string? MiddleName { get; set; }

    [EmailAddress]
    [MaxLength(256)]
    public string? Email { get; set; }

    [Phone]
    [MaxLength(20)]
    public string? PhoneNumber { get; set; }

    [Phone]
    [MaxLength(20)]
    public string? WorkPhone { get; set; }

    [MaxLength(100)]
    public string? Occupation { get; set; }

    [MaxLength(100)]
    public string? Employer { get; set; }

    [MaxLength(500)]
    public string? Address { get; set; }

    [MaxLength(100)]
    public string? City { get; set; }

    [MaxLength(100)]
    public string? State { get; set; }

    [MaxLength(20)]
    public string? PostalCode { get; set; }

    [MaxLength(100)]
    public string? Country { get; set; }

    [MaxLength(50)]
    public string? NationalId { get; set; }

    public DateTime? DateOfBirth { get; set; }

    [MaxLength(10)]
    public string? Gender { get; set; }

    [Required]
    [MaxLength(50)]
    public string Relationship { get; set; } = string.Empty;

    public bool IsPrimaryContact { get; set; } = false;

    public bool IsEmergencyContact { get; set; } = false;

    public bool CanPickupStudent { get; set; } = true;

    public bool ReceiveReports { get; set; } = true;

    public bool ReceiveNotifications { get; set; } = true;
}

/// <summary>
/// DTO for guardian response
/// </summary>
public class GuardianDto
{
    public Guid Id { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string? MiddleName { get; set; }
    public string FullName { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string? Email { get; set; }
    public string? PhoneNumber { get; set; }
    public string? WorkPhone { get; set; }
    public string? Occupation { get; set; }
    public string? Employer { get; set; }
    public string? Address { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? PostalCode { get; set; }
    public string? Country { get; set; }
    public string? NationalId { get; set; }
    public DateTime? DateOfBirth { get; set; }
    public string? Gender { get; set; }
    public string Relationship { get; set; } = string.Empty;
    public bool IsPrimaryContact { get; set; }
    public bool IsEmergencyContact { get; set; }
    public bool CanPickupStudent { get; set; }
    public bool ReceiveReports { get; set; }
    public bool ReceiveNotifications { get; set; }
}

/// <summary>
/// DTO for student enrollment
/// </summary>
public class StudentEnrollmentDto
{
    public Guid Id { get; set; }
    public Guid StudentId { get; set; }
    public Guid GradeId { get; set; }
    public string GradeName { get; set; } = string.Empty;
    public Guid AcademicYearId { get; set; }
    public string AcademicYearName { get; set; } = string.Empty;
    public DateTime EnrollmentDate { get; set; }
    public DateTime? WithdrawalDate { get; set; }
    public EnrollmentStatus Status { get; set; }
    public string? Notes { get; set; }
}
