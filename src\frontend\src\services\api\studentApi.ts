import { apiHelpers, PaginatedResponse } from './apiClient';
import { Student, Guardian, StudentEnrollment } from '../../store/slices/studentSlice';

export interface CreateStudentRequest {
  studentNumber: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  arabicFirstName?: string;
  arabicLastName?: string;
  arabicMiddleName?: string;
  dateOfBirth: string;
  gender: string;
  nationality?: string;
  nationalId?: string;
  passportNumber?: string;
  placeOfBirth?: string;
  bloodType?: string;
  emergencyContactPhone?: string;
  emergencyContactName?: string;
  emergencyContactRelation?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  enrollmentDate?: string;
  notes?: string;
  medicalInfo?: any;
  specialNeeds?: string;
  transportationInfo?: string;
  email: string;
  phoneNumber?: string;
  guardians?: Array<{
    firstName: string;
    lastName: string;
    middleName?: string;
    email?: string;
    phoneNumber?: string;
    workPhone?: string;
    occupation?: string;
    employer?: string;
    address?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
    nationalId?: string;
    dateOfBirth?: string;
    gender?: string;
    relationship: string;
    isPrimaryContact?: boolean;
    isEmergencyContact?: boolean;
    canPickupStudent?: boolean;
    receiveReports?: boolean;
    receiveNotifications?: boolean;
  }>;
}

export interface UpdateStudentRequest {
  id: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  arabicFirstName?: string;
  arabicLastName?: string;
  arabicMiddleName?: string;
  dateOfBirth: string;
  gender: string;
  nationality?: string;
  nationalId?: string;
  passportNumber?: string;
  placeOfBirth?: string;
  bloodType?: string;
  emergencyContactPhone?: string;
  emergencyContactName?: string;
  emergencyContactRelation?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  status: string;
  notes?: string;
  medicalInfo?: any;
  specialNeeds?: string;
  transportationInfo?: string;
  email: string;
  phoneNumber?: string;
}

export interface StudentFilters {
  search?: string;
  grade?: string;
  status?: string;
  gender?: string;
  nationality?: string;
  enrollmentYear?: string;
  age?: { min?: number; max?: number };
}

export interface EnrollStudentRequest {
  studentId: string;
  gradeId: string;
  academicYearId: string;
  enrollmentDate?: string;
  notes?: string;
}

export const studentApi = {
  // Get students with pagination and filters
  getStudents: async (params?: {
    page?: number;
    pageSize?: number;
    filters?: StudentFilters;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<PaginatedResponse<Student>> => {
    return apiHelpers.get('/students', { params });
  },

  // Get student by ID
  getStudentById: async (id: string): Promise<Student> => {
    return apiHelpers.get(`/students/${id}`);
  },

  // Get student by student number
  getStudentByNumber: async (studentNumber: string): Promise<Student> => {
    return apiHelpers.get(`/students/by-number/${studentNumber}`);
  },

  // Create new student
  createStudent: async (studentData: CreateStudentRequest): Promise<Student> => {
    return apiHelpers.post('/students', studentData);
  },

  // Update student
  updateStudent: async (id: string, studentData: UpdateStudentRequest): Promise<Student> => {
    return apiHelpers.put(`/students/${id}`, studentData);
  },

  // Delete student
  deleteStudent: async (id: string): Promise<void> => {
    return apiHelpers.delete(`/students/${id}`);
  },

  // Get students by grade
  getStudentsByGrade: async (gradeId: string): Promise<Student[]> => {
    return apiHelpers.get(`/students/by-grade/${gradeId}`);
  },

  // Enroll student in grade
  enrollStudent: async (enrollmentData: EnrollStudentRequest): Promise<StudentEnrollment> => {
    return apiHelpers.post('/students/enroll', enrollmentData);
  },

  // Get student enrollments
  getStudentEnrollments: async (studentId: string): Promise<StudentEnrollment[]> => {
    return apiHelpers.get(`/students/${studentId}/enrollments`);
  },

  // Withdraw student from grade
  withdrawStudent: async (enrollmentId: string, reason?: string): Promise<void> => {
    return apiHelpers.post(`/students/enrollments/${enrollmentId}/withdraw`, { reason });
  },

  // Get student guardians
  getStudentGuardians: async (studentId: string): Promise<Guardian[]> => {
    return apiHelpers.get(`/students/${studentId}/guardians`);
  },

  // Add guardian to student
  addGuardianToStudent: async (studentId: string, guardianData: {
    firstName: string;
    lastName: string;
    middleName?: string;
    email?: string;
    phoneNumber?: string;
    workPhone?: string;
    occupation?: string;
    employer?: string;
    address?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
    nationalId?: string;
    dateOfBirth?: string;
    gender?: string;
    relationship: string;
    isPrimaryContact?: boolean;
    isEmergencyContact?: boolean;
    canPickupStudent?: boolean;
    receiveReports?: boolean;
    receiveNotifications?: boolean;
  }): Promise<Guardian> => {
    return apiHelpers.post(`/students/${studentId}/guardians`, guardianData);
  },

  // Update guardian
  updateGuardian: async (guardianId: string, guardianData: Partial<Guardian>): Promise<Guardian> => {
    return apiHelpers.put(`/guardians/${guardianId}`, guardianData);
  },

  // Remove guardian from student
  removeGuardianFromStudent: async (studentId: string, guardianId: string): Promise<void> => {
    return apiHelpers.delete(`/students/${studentId}/guardians/${guardianId}`);
  },

  // Upload student profile picture
  uploadStudentProfilePicture: async (studentId: string, file: File): Promise<{ profilePictureUrl: string }> => {
    return apiHelpers.upload(`/students/${studentId}/profile-picture`, file);
  },

  // Get student documents
  getStudentDocuments: async (studentId: string): Promise<Array<{
    id: string;
    documentType: string;
    fileName: string;
    filePath: string;
    contentType: string;
    fileSize: number;
    description?: string;
    expiryDate?: string;
    isRequired: boolean;
    isVerified: boolean;
    verifiedAt?: string;
    verifiedBy?: string;
    uploadedAt: string;
  }>> => {
    return apiHelpers.get(`/students/${studentId}/documents`);
  },

  // Upload student document
  uploadStudentDocument: async (studentId: string, file: File, documentType: string, description?: string): Promise<{
    id: string;
    fileName: string;
    filePath: string;
    documentType: string;
  }> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('documentType', documentType);
    if (description) {
      formData.append('description', description);
    }
    return apiHelpers.post(`/students/${studentId}/documents`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },

  // Delete student document
  deleteStudentDocument: async (studentId: string, documentId: string): Promise<void> => {
    return apiHelpers.delete(`/students/${studentId}/documents/${documentId}`);
  },

  // Verify student document
  verifyStudentDocument: async (studentId: string, documentId: string): Promise<void> => {
    return apiHelpers.post(`/students/${studentId}/documents/${documentId}/verify`);
  },

  // Get student academic history
  getStudentAcademicHistory: async (studentId: string): Promise<Array<{
    academicYear: string;
    grade: string;
    subjects: Array<{
      subjectName: string;
      finalGrade: number;
      letterGrade: string;
      credits: number;
    }>;
    gpa: number;
    rank?: number;
    totalStudents?: number;
  }>> => {
    return apiHelpers.get(`/students/${studentId}/academic-history`);
  },

  // Get student attendance summary
  getStudentAttendanceSummary: async (studentId: string, academicYearId?: string): Promise<{
    totalDays: number;
    presentDays: number;
    absentDays: number;
    lateDays: number;
    attendancePercentage: number;
    monthlyBreakdown: Array<{
      month: string;
      totalDays: number;
      presentDays: number;
      attendancePercentage: number;
    }>;
  }> => {
    return apiHelpers.get(`/students/${studentId}/attendance-summary`, {
      params: { academicYearId }
    });
  },

  // Get student financial summary
  getStudentFinancialSummary: async (studentId: string, academicYearId?: string): Promise<{
    totalFees: number;
    paidAmount: number;
    pendingAmount: number;
    overdueAmount: number;
    discountAmount: number;
    currency: string;
    paymentHistory: Array<{
      date: string;
      amount: number;
      paymentMethod: string;
      reference: string;
    }>;
  }> => {
    return apiHelpers.get(`/students/${studentId}/financial-summary`, {
      params: { academicYearId }
    });
  },

  // Generate student report card
  generateStudentReportCard: async (studentId: string, termId: string): Promise<void> => {
    return apiHelpers.download(`/students/${studentId}/report-card/${termId}`, `report-card-${studentId}.pdf`);
  },

  // Export students data
  exportStudents: async (filters?: StudentFilters, format: 'csv' | 'excel' = 'excel'): Promise<void> => {
    return apiHelpers.download('/students/export', `students.${format}`, {
      params: { ...filters, format }
    });
  },

  // Import students data
  importStudents: async (file: File): Promise<{
    message: string;
    importedCount: number;
    errors?: Array<{
      row: number;
      field: string;
      message: string;
    }>;
  }> => {
    return apiHelpers.upload('/students/import', file);
  },

  // Bulk update students
  bulkUpdateStudents: async (studentIds: string[], updates: Partial<UpdateStudentRequest>): Promise<{
    updatedCount: number;
    errors?: Array<{
      studentId: string;
      message: string;
    }>;
  }> => {
    return apiHelpers.post('/students/bulk-update', { studentIds, updates });
  },

  // Transfer student to another grade
  transferStudent: async (studentId: string, newGradeId: string, transferDate?: string, reason?: string): Promise<StudentEnrollment> => {
    return apiHelpers.post(`/students/${studentId}/transfer`, {
      newGradeId,
      transferDate,
      reason
    });
  },

  // Promote students to next grade
  promoteStudents: async (currentGradeId: string, nextGradeId: string, studentIds?: string[]): Promise<{
    promotedCount: number;
    errors?: Array<{
      studentId: string;
      message: string;
    }>;
  }> => {
    return apiHelpers.post('/students/promote', {
      currentGradeId,
      nextGradeId,
      studentIds
    });
  },
};
