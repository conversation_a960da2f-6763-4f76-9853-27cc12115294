import { apiHelpers, PaginatedResponse } from './apiClient';
import { FeeStructure, StudentFee, Payment, Invoice, FinancialReport } from '../../store/slices/financeSlice';

export const financeApi = {
  // Fee Structures
  getFeeStructures: async (academicYearId?: string): Promise<FeeStructure[]> => {
    return apiHelpers.get('/finance/fee-structures', { params: { academicYearId } });
  },

  getFeeStructureById: async (id: string): Promise<FeeStructure> => {
    return apiHelpers.get(`/finance/fee-structures/${id}`);
  },

  createFeeStructure: async (data: {
    name: string;
    description?: string;
    academicYearId: string;
    gradeId?: string;
    feeType: string;
    amount: number;
    currency?: string;
    paymentFrequency: string;
    dueDate?: string;
    isOptional?: boolean;
    discountPercentage?: number;
    lateFeeAmount?: number;
    lateFeeGraceDays?: number;
  }): Promise<FeeStructure> => {
    return apiHelpers.post('/finance/fee-structures', data);
  },

  updateFeeStructure: async (id: string, data: Partial<FeeStructure>): Promise<FeeStructure> => {
    return apiHelpers.put(`/finance/fee-structures/${id}`, data);
  },

  deleteFeeStructure: async (id: string): Promise<void> => {
    return apiHelpers.delete(`/finance/fee-structures/${id}`);
  },

  // Student Fees
  getStudentFees: async (studentId: string): Promise<StudentFee[]> => {
    return apiHelpers.get(`/finance/student-fees`, { params: { studentId } });
  },

  getStudentFeeById: async (id: string): Promise<StudentFee> => {
    return apiHelpers.get(`/finance/student-fees/${id}`);
  },

  createStudentFee: async (data: {
    studentId: string;
    feeStructureId: string;
    amount: number;
    currency?: string;
    dueDate: string;
    discountAmount?: number;
    notes?: string;
  }): Promise<StudentFee> => {
    return apiHelpers.post('/finance/student-fees', data);
  },

  updateStudentFee: async (id: string, data: Partial<StudentFee>): Promise<StudentFee> => {
    return apiHelpers.put(`/finance/student-fees/${id}`, data);
  },

  deleteStudentFee: async (id: string): Promise<void> => {
    return apiHelpers.delete(`/finance/student-fees/${id}`);
  },

  bulkCreateStudentFees: async (data: {
    feeStructureId: string;
    studentIds: string[];
    dueDate: string;
    discountPercentage?: number;
  }): Promise<{
    createdCount: number;
    errors?: Array<{
      studentId: string;
      message: string;
    }>;
  }> => {
    return apiHelpers.post('/finance/student-fees/bulk', data);
  },

  // Payments
  getPayments: async (params?: {
    page?: number;
    pageSize?: number;
    studentId?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
    paymentMethod?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<PaginatedResponse<Payment>> => {
    return apiHelpers.get('/finance/payments', { params });
  },

  getPaymentById: async (id: string): Promise<Payment> => {
    return apiHelpers.get(`/finance/payments/${id}`);
  },

  processPayment: async (data: {
    studentId: string;
    studentFeeId?: string;
    amount: number;
    currency?: string;
    paymentMethod: string;
    transactionReference?: string;
    bankReference?: string;
    notes?: string;
    feeAllocations?: Array<{
      studentFeeId: string;
      amount: number;
    }>;
  }): Promise<Payment> => {
    return apiHelpers.post('/finance/payments', data);
  },

  updatePayment: async (id: string, data: Partial<Payment>): Promise<Payment> => {
    return apiHelpers.put(`/finance/payments/${id}`, data);
  },

  cancelPayment: async (id: string, reason: string): Promise<void> => {
    return apiHelpers.post(`/finance/payments/${id}/cancel`, { reason });
  },

  refundPayment: async (id: string, amount: number, reason: string): Promise<{
    refundId: string;
    refundAmount: number;
    message: string;
  }> => {
    return apiHelpers.post(`/finance/payments/${id}/refund`, { amount, reason });
  },

  getPaymentHistory: async (studentId: string): Promise<Payment[]> => {
    return apiHelpers.get(`/finance/payments/history/${studentId}`);
  },

  // Invoices
  getInvoices: async (studentId?: string): Promise<Invoice[]> => {
    return apiHelpers.get('/finance/invoices', { params: { studentId } });
  },

  getInvoiceById: async (id: string): Promise<Invoice> => {
    return apiHelpers.get(`/finance/invoices/${id}`);
  },

  generateInvoice: async (studentId: string, feeIds: string[]): Promise<Invoice> => {
    return apiHelpers.post('/finance/invoices/generate', { studentId, feeIds });
  },

  updateInvoice: async (id: string, data: Partial<Invoice>): Promise<Invoice> => {
    return apiHelpers.put(`/finance/invoices/${id}`, data);
  },

  sendInvoice: async (id: string, email?: string): Promise<{ message: string }> => {
    return apiHelpers.post(`/finance/invoices/${id}/send`, { email });
  },

  markInvoiceAsPaid: async (id: string, paymentId: string): Promise<void> => {
    return apiHelpers.post(`/finance/invoices/${id}/mark-paid`, { paymentId });
  },

  cancelInvoice: async (id: string, reason: string): Promise<void> => {
    return apiHelpers.post(`/finance/invoices/${id}/cancel`, { reason });
  },

  downloadInvoice: async (id: string): Promise<void> => {
    return apiHelpers.download(`/finance/invoices/${id}/download`, `invoice-${id}.pdf`);
  },

  // Discounts
  getDiscounts: async (): Promise<Array<{
    id: string;
    name: string;
    description?: string;
    type: string;
    value: number;
    isPercentage: boolean;
    validFrom?: string;
    validTo?: string;
    isActive: boolean;
    maxUsage?: number;
    currentUsage: number;
  }>> => {
    return apiHelpers.get('/finance/discounts');
  },

  createDiscount: async (data: {
    name: string;
    description?: string;
    type: string;
    value: number;
    isPercentage: boolean;
    validFrom?: string;
    validTo?: string;
    conditions?: string;
    maxUsage?: number;
  }): Promise<{
    id: string;
    message: string;
  }> => {
    return apiHelpers.post('/finance/discounts', data);
  },

  applyDiscountToStudent: async (studentId: string, discountId: string, notes?: string): Promise<{
    message: string;
    discountAmount: number;
  }> => {
    return apiHelpers.post(`/finance/discounts/${discountId}/apply`, { studentId, notes });
  },

  // Financial Reports
  getFinancialReports: async (): Promise<FinancialReport[]> => {
    return apiHelpers.get('/finance/reports');
  },

  generateFinancialReport: async (
    reportType: string,
    periodStart: string,
    periodEnd: string,
    filters?: Record<string, any>
  ): Promise<FinancialReport> => {
    return apiHelpers.post('/finance/reports/generate', {
      reportType,
      periodStart,
      periodEnd,
      filters
    });
  },

  downloadFinancialReport: async (id: string): Promise<void> => {
    return apiHelpers.download(`/finance/reports/${id}/download`, `financial-report-${id}.pdf`);
  },

  // Financial Dashboard
  getFinancialDashboard: async (period?: string): Promise<{
    totalRevenue: number;
    pendingAmount: number;
    overdueAmount: number;
    collectionRate: number;
    monthlyRevenue: Array<{
      month: string;
      revenue: number;
      collections: number;
    }>;
    feeTypeBreakdown: Array<{
      feeType: string;
      amount: number;
      percentage: number;
    }>;
    paymentMethodBreakdown: Array<{
      method: string;
      amount: number;
      count: number;
    }>;
    overdueAnalysis: Array<{
      daysOverdue: string;
      count: number;
      amount: number;
    }>;
  }> => {
    return apiHelpers.get('/finance/dashboard', { params: { period } });
  },

  // Fee Collection
  getOutstandingFees: async (filters?: {
    gradeId?: string;
    feeType?: string;
    daysOverdue?: number;
  }): Promise<Array<{
    studentId: string;
    studentName: string;
    grade: string;
    totalOutstanding: number;
    overdueAmount: number;
    daysOverdue: number;
    fees: Array<{
      feeId: string;
      feeType: string;
      amount: number;
      dueDate: string;
    }>;
  }>> => {
    return apiHelpers.get('/finance/outstanding-fees', { params: filters });
  },

  sendPaymentReminders: async (studentIds: string[], reminderType: 'email' | 'sms' | 'both'): Promise<{
    sentCount: number;
    errors?: Array<{
      studentId: string;
      message: string;
    }>;
  }> => {
    return apiHelpers.post('/finance/payment-reminders', { studentIds, reminderType });
  },

  // Expenses
  getExpenses: async (params?: {
    page?: number;
    pageSize?: number;
    category?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
  }): Promise<PaginatedResponse<{
    id: string;
    description: string;
    category: string;
    amount: number;
    currency: string;
    expenseDate: string;
    vendor?: string;
    invoiceNumber?: string;
    paymentMethod?: string;
    status: string;
    notes?: string;
    approvedBy?: string;
    approvedAt?: string;
    receiptPath?: string;
  }>> => {
    return apiHelpers.get('/finance/expenses', { params });
  },

  createExpense: async (data: {
    description: string;
    category: string;
    amount: number;
    currency?: string;
    expenseDate: string;
    vendor?: string;
    invoiceNumber?: string;
    paymentMethod?: string;
    notes?: string;
  }): Promise<{
    id: string;
    message: string;
  }> => {
    return apiHelpers.post('/finance/expenses', data);
  },

  approveExpense: async (id: string): Promise<void> => {
    return apiHelpers.post(`/finance/expenses/${id}/approve`);
  },

  rejectExpense: async (id: string, reason: string): Promise<void> => {
    return apiHelpers.post(`/finance/expenses/${id}/reject`, { reason });
  },

  uploadExpenseReceipt: async (id: string, file: File): Promise<{ receiptPath: string }> => {
    return apiHelpers.upload(`/finance/expenses/${id}/receipt`, file);
  },

  // Budget Management
  getBudgets: async (year?: number): Promise<Array<{
    id: string;
    category: string;
    budgetedAmount: number;
    spentAmount: number;
    remainingAmount: number;
    percentage: number;
    year: number;
    month?: number;
  }>> => {
    return apiHelpers.get('/finance/budgets', { params: { year } });
  },

  createBudget: async (data: {
    category: string;
    budgetedAmount: number;
    year: number;
    month?: number;
    description?: string;
  }): Promise<{
    id: string;
    message: string;
  }> => {
    return apiHelpers.post('/finance/budgets', data);
  },

  updateBudget: async (id: string, data: {
    budgetedAmount: number;
    description?: string;
  }): Promise<void> => {
    return apiHelpers.put(`/finance/budgets/${id}`, data);
  },
};
