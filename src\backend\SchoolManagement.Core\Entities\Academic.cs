using System.ComponentModel.DataAnnotations;

namespace SchoolManagement.Core.Entities;

/// <summary>
/// Represents an academic year
/// </summary>
public class AcademicYear : TenantEntity
{
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    public DateTime StartDate { get; set; }
    
    [Required]
    public DateTime EndDate { get; set; }
    
    public bool IsActive { get; set; } = false;
    
    public bool IsCurrent { get; set; } = false;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    // Navigation properties
    public virtual ICollection<Term> Terms { get; set; } = new List<Term>();
    public virtual ICollection<StudentEnrollment> Enrollments { get; set; } = new List<StudentEnrollment>();
    public virtual ICollection<ClassSchedule> ClassSchedules { get; set; } = new List<ClassSchedule>();
}

/// <summary>
/// Represents a term/semester within an academic year
/// </summary>
public class Term : TenantEntity
{
    [Required]
    public Guid AcademicYearId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    public DateTime StartDate { get; set; }
    
    [Required]
    public DateTime EndDate { get; set; }
    
    public bool IsActive { get; set; } = false;
    
    public bool IsCurrent { get; set; } = false;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    // Navigation properties
    public virtual AcademicYear AcademicYear { get; set; } = null!;
    public virtual ICollection<StudentGrade> StudentGrades { get; set; } = new List<StudentGrade>();
    public virtual ICollection<Exam> Exams { get; set; } = new List<Exam>();
}

/// <summary>
/// Represents a grade/class level
/// </summary>
public class Grade : TenantEntity
{
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    public int Level { get; set; } // 1, 2, 3, etc.
    
    [MaxLength(100)]
    public string? Section { get; set; } // A, B, C, etc.
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    public int MaxStudents { get; set; } = 30;
    
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual ICollection<StudentEnrollment> Enrollments { get; set; } = new List<StudentEnrollment>();
    public virtual ICollection<ClassSchedule> ClassSchedules { get; set; } = new List<ClassSchedule>();
    public virtual ICollection<GradeSubject> GradeSubjects { get; set; } = new List<GradeSubject>();
}

/// <summary>
/// Represents a subject
/// </summary>
public class Subject : TenantEntity
{
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? ArabicName { get; set; }
    
    [Required]
    [MaxLength(20)]
    public string Code { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    public int CreditHours { get; set; } = 1;
    
    public bool IsActive { get; set; } = true;
    
    public bool IsCore { get; set; } = true; // Core vs Elective
    
    [MaxLength(100)]
    public string? Department { get; set; }
    
    // Navigation properties
    public virtual ICollection<GradeSubject> GradeSubjects { get; set; } = new List<GradeSubject>();
    public virtual ICollection<ClassSchedule> ClassSchedules { get; set; } = new List<ClassSchedule>();
    public virtual ICollection<StudentGrade> StudentGrades { get; set; } = new List<StudentGrade>();
    public virtual ICollection<Exam> Exams { get; set; } = new List<Exam>();
}

/// <summary>
/// Many-to-many relationship between grades and subjects
/// </summary>
public class GradeSubject : TenantEntity
{
    [Required]
    public Guid GradeId { get; set; }
    
    [Required]
    public Guid SubjectId { get; set; }
    
    public int WeeklyHours { get; set; } = 1;
    
    public bool IsRequired { get; set; } = true;
    
    public int PassingGrade { get; set; } = 60;
    
    public int MaxGrade { get; set; } = 100;
    
    // Navigation properties
    public virtual Grade Grade { get; set; } = null!;
    public virtual Subject Subject { get; set; } = null!;
}

/// <summary>
/// Student enrollment in a grade for an academic year
/// </summary>
public class StudentEnrollment : TenantEntity
{
    [Required]
    public Guid StudentId { get; set; }
    
    [Required]
    public Guid GradeId { get; set; }
    
    [Required]
    public Guid AcademicYearId { get; set; }
    
    public DateTime EnrollmentDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? WithdrawalDate { get; set; }
    
    public EnrollmentStatus Status { get; set; } = EnrollmentStatus.Active;
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    // Navigation properties
    public virtual Student Student { get; set; } = null!;
    public virtual Grade Grade { get; set; } = null!;
    public virtual AcademicYear AcademicYear { get; set; } = null!;
}

/// <summary>
/// Enrollment status enumeration
/// </summary>
public enum EnrollmentStatus
{
    Active,
    Withdrawn,
    Transferred,
    Completed,
    Suspended
}

/// <summary>
/// Class schedule/timetable
/// </summary>
public class ClassSchedule : TenantEntity
{
    [Required]
    public Guid GradeId { get; set; }
    
    [Required]
    public Guid SubjectId { get; set; }
    
    [Required]
    public Guid AcademicYearId { get; set; }
    
    public Guid? StaffId { get; set; }
    
    [Required]
    [MaxLength(20)]
    public string DayOfWeek { get; set; } = string.Empty; // Monday, Tuesday, etc.
    
    [Required]
    public TimeSpan StartTime { get; set; }
    
    [Required]
    public TimeSpan EndTime { get; set; }
    
    [MaxLength(100)]
    public string? Classroom { get; set; }
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual Grade Grade { get; set; } = null!;
    public virtual Subject Subject { get; set; } = null!;
    public virtual AcademicYear AcademicYear { get; set; } = null!;
    public virtual Staff? Staff { get; set; }
}

/// <summary>
/// Student grades/marks
/// </summary>
public class StudentGrade : TenantEntity
{
    [Required]
    public Guid StudentId { get; set; }
    
    [Required]
    public Guid SubjectId { get; set; }
    
    [Required]
    public Guid TermId { get; set; }
    
    public Guid? ExamId { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string GradeType { get; set; } = string.Empty; // Quiz, Midterm, Final, Assignment, etc.
    
    public decimal Score { get; set; }
    
    public decimal MaxScore { get; set; } = 100;
    
    public decimal Percentage => MaxScore > 0 ? (Score / MaxScore) * 100 : 0;
    
    [MaxLength(10)]
    public string? LetterGrade { get; set; } // A, B, C, D, F
    
    [MaxLength(500)]
    public string? Comments { get; set; }
    
    public DateTime GradeDate { get; set; } = DateTime.UtcNow;
    
    public string? GradedBy { get; set; }
    
    // Navigation properties
    public virtual Student Student { get; set; } = null!;
    public virtual Subject Subject { get; set; } = null!;
    public virtual Term Term { get; set; } = null!;
    public virtual Exam? Exam { get; set; }
}

/// <summary>
/// Exams
/// </summary>
public class Exam : TenantEntity
{
    [Required]
    public Guid SubjectId { get; set; }
    
    [Required]
    public Guid TermId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(50)]
    public string Type { get; set; } = string.Empty; // Quiz, Midterm, Final, etc.
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    public DateTime ExamDate { get; set; }
    
    public TimeSpan Duration { get; set; }
    
    public decimal MaxScore { get; set; } = 100;
    
    public decimal PassingScore { get; set; } = 60;
    
    [MaxLength(100)]
    public string? Classroom { get; set; }
    
    [MaxLength(500)]
    public string? Instructions { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual Subject Subject { get; set; } = null!;
    public virtual Term Term { get; set; } = null!;
    public virtual ICollection<StudentGrade> StudentGrades { get; set; } = new List<StudentGrade>();
}
