import React, { useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box, Typography } from '@mui/material';
import { useAppDispatch } from '../../store/store';
import { setPageTitle, setBreadcrumbs } from '../../store/slices/uiSlice';

const ReportsListPage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setPageTitle('Reports'));
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', path: '/dashboard' },
      { label: 'Reports' }
    ]));
  }, [dispatch]);

  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 700, mb: 3 }}>
        Reports & Analytics
      </Typography>
      <Typography variant="body1" color="text.secondary">
        Reports and analytics functionality will be implemented here.
      </Typography>
    </Box>
  );
};

const ReportsPage: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<ReportsListPage />} />
      <Route path="/academic" element={<ReportsListPage />} />
      <Route path="/financial" element={<ReportsListPage />} />
      <Route path="/attendance" element={<ReportsListPage />} />
    </Routes>
  );
};

export default ReportsPage;
