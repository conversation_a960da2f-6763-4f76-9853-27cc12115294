{"name": "school-management-ui", "version": "1.0.0", "description": "School Management System Frontend", "private": true, "dependencies": {"@reduxjs/toolkit": "^2.0.1", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "typescript": "^5.3.3", "web-vitals": "^3.5.0", "@mui/material": "^5.15.2", "@mui/icons-material": "^5.15.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-data-grid": "^6.18.2", "@mui/x-date-pickers": "^6.18.2", "@mui/lab": "^5.0.0-alpha.158", "axios": "^1.6.2", "formik": "^2.4.5", "yup": "^1.4.0", "react-query": "^3.39.3", "react-hot-toast": "^2.4.1", "date-fns": "^3.0.6", "recharts": "^2.8.0", "react-helmet-async": "^2.0.4", "i18next": "^23.7.6", "react-i18next": "^13.5.0", "i18next-browser-languagedetector": "^7.2.0", "react-dropzone": "^14.2.3", "react-pdf": "^7.6.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "eslint": "^8.55.0", "eslint-config-react-app": "^7.0.1", "prettier": "^3.1.1", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,json,css,md}", "prepare": "husky install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix", "prettier --write"], "src/**/*.{json,css,md}": ["prettier --write"]}, "proxy": "http://localhost:5000"}