using Microsoft.EntityFrameworkCore;
using SchoolManagement.Core.Entities;
using SchoolManagement.Core.Interfaces;
using System.Reflection;

namespace SchoolManagement.Infrastructure.Data;

/// <summary>
/// Main database context for the school management system
/// </summary>
public class SchoolManagementDbContext : DbContext
{
    private readonly ITenantContext _tenantContext;

    public SchoolManagementDbContext(DbContextOptions<SchoolManagementDbContext> options, ITenantContext tenantContext)
        : base(options)
    {
        _tenantContext = tenantContext;
    }

    // Core entities
    public DbSet<Tenant> Tenants { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<Permission> Permissions { get; set; }
    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<RolePermission> RolePermissions { get; set; }
    public DbSet<UserPermission> UserPermissions { get; set; }
    public DbSet<AuditLog> AuditLogs { get; set; }

    // Student management
    public DbSet<Student> Students { get; set; }
    public DbSet<Guardian> Guardians { get; set; }
    public DbSet<StudentGuardian> StudentGuardians { get; set; }
    public DbSet<StudentDocument> StudentDocuments { get; set; }

    // Staff management
    public DbSet<Staff> Staff { get; set; }
    public DbSet<StaffDocument> StaffDocuments { get; set; }
    public DbSet<StaffLeave> StaffLeaves { get; set; }
    public DbSet<StaffAttendance> StaffAttendances { get; set; }
    public DbSet<Payroll> Payrolls { get; set; }

    // Academic management
    public DbSet<AcademicYear> AcademicYears { get; set; }
    public DbSet<Term> Terms { get; set; }
    public DbSet<Grade> Grades { get; set; }
    public DbSet<Subject> Subjects { get; set; }
    public DbSet<GradeSubject> GradeSubjects { get; set; }
    public DbSet<StudentEnrollment> StudentEnrollments { get; set; }
    public DbSet<ClassSchedule> ClassSchedules { get; set; }
    public DbSet<StudentGrade> StudentGrades { get; set; }
    public DbSet<Exam> Exams { get; set; }

    // Financial management
    public DbSet<FeeStructure> FeeStructures { get; set; }
    public DbSet<StudentFee> StudentFees { get; set; }
    public DbSet<Payment> Payments { get; set; }
    public DbSet<PaymentAllocation> PaymentAllocations { get; set; }
    public DbSet<Discount> Discounts { get; set; }
    public DbSet<StudentDiscount> StudentDiscounts { get; set; }
    public DbSet<FinancialReport> FinancialReports { get; set; }
    public DbSet<Expense> Expenses { get; set; }
    public DbSet<Invoice> Invoices { get; set; }
    public DbSet<InvoiceItem> InvoiceItems { get; set; }

    // Attendance management
    public DbSet<StudentAttendance> StudentAttendances { get; set; }
    public DbSet<AttendanceSummary> AttendanceSummaries { get; set; }
    public DbSet<AttendancePolicy> AttendancePolicies { get; set; }
    public DbSet<StudentLeave> StudentLeaves { get; set; }
    public DbSet<AttendanceNotification> AttendanceNotifications { get; set; }
    public DbSet<AttendanceDevice> AttendanceDevices { get; set; }
    public DbSet<AttendanceDeviceLog> AttendanceDeviceLogs { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply all configurations from the current assembly
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

        // Configure global query filters for multi-tenancy
        ConfigureGlobalQueryFilters(modelBuilder);

        // Configure row-level security
        ConfigureRowLevelSecurity(modelBuilder);
    }

    private void ConfigureGlobalQueryFilters(ModelBuilder modelBuilder)
    {
        // Apply global query filter for tenant entities
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (typeof(TenantEntity).IsAssignableFrom(entityType.ClrType))
            {
                var method = typeof(SchoolManagementDbContext)
                    .GetMethod(nameof(GetTenantFilter), BindingFlags.NonPublic | BindingFlags.Static)!
                    .MakeGenericMethod(entityType.ClrType);

                var filter = method.Invoke(null, new object[] { _tenantContext });
                entityType.SetQueryFilter((LambdaExpression)filter!);
            }

            // Apply global query filter for soft delete
            if (typeof(BaseEntity).IsAssignableFrom(entityType.ClrType))
            {
                var method = typeof(SchoolManagementDbContext)
                    .GetMethod(nameof(GetSoftDeleteFilter), BindingFlags.NonPublic | BindingFlags.Static)!
                    .MakeGenericMethod(entityType.ClrType);

                var filter = method.Invoke(null, null);
                entityType.SetQueryFilter((LambdaExpression)filter!);
            }
        }
    }

    private static LambdaExpression GetTenantFilter<TEntity>(ITenantContext tenantContext)
        where TEntity : class, TenantEntity
    {
        return (Expression<Func<TEntity, bool>>)(e => !tenantContext.HasTenant || e.TenantId == tenantContext.TenantId);
    }

    private static LambdaExpression GetSoftDeleteFilter<TEntity>()
        where TEntity : class, BaseEntity
    {
        return (Expression<Func<TEntity, bool>>)(e => !e.IsDeleted);
    }

    private void ConfigureRowLevelSecurity(ModelBuilder modelBuilder)
    {
        // Configure row-level security for SQL Server
        // This will be implemented in migrations
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Set audit fields before saving
        SetAuditFields();

        // Set tenant ID for new entities
        SetTenantId();

        return await base.SaveChangesAsync(cancellationToken);
    }

    private void SetAuditFields()
    {
        var entries = ChangeTracker.Entries<BaseEntity>();

        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = DateTime.UtcNow;
                    entry.Entity.CreatedBy = GetCurrentUserId();
                    break;

                case EntityState.Modified:
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    entry.Entity.UpdatedBy = GetCurrentUserId();
                    break;

                case EntityState.Deleted:
                    entry.State = EntityState.Modified;
                    entry.Entity.IsDeleted = true;
                    entry.Entity.DeletedAt = DateTime.UtcNow;
                    entry.Entity.DeletedBy = GetCurrentUserId();
                    break;
            }
        }
    }

    private void SetTenantId()
    {
        if (!_tenantContext.HasTenant) return;

        var entries = ChangeTracker.Entries<TenantEntity>()
            .Where(e => e.State == EntityState.Added);

        foreach (var entry in entries)
        {
            entry.Entity.TenantId = _tenantContext.TenantId!.Value;
        }
    }

    private string? GetCurrentUserId()
    {
        // This should be implemented to get the current user ID from the authentication context
        // For now, return null - this will be implemented in the API layer
        return null;
    }
}
