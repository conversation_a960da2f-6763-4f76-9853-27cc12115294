import { apiHelpers } from './apiClient';
import { User } from '../../store/slices/authSlice';

export interface LoginRequest {
  email: string;
  password: string;
  tenantCode?: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RegisterRequest {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  tenantCode?: string;
}

export interface RegisterResponse {
  user: User;
  token: string;
  refreshToken: string;
  message: string;
}

export interface ForgotPasswordRequest {
  email: string;
  tenantCode?: string;
}

export interface ResetPasswordRequest {
  token: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  gender?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
}

export const authApi = {
  // Login user
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    return apiHelpers.post('/auth/login', credentials);
  },

  // Register new user
  register: async (userData: RegisterRequest): Promise<RegisterResponse> => {
    return apiHelpers.post('/auth/register', userData);
  },

  // Logout user
  logout: async (): Promise<void> => {
    return apiHelpers.post('/auth/logout');
  },

  // Refresh access token
  refreshToken: async (refreshToken: string): Promise<RefreshTokenResponse> => {
    return apiHelpers.post('/auth/refresh-token', { refreshToken });
  },

  // Get current user information
  getCurrentUser: async (): Promise<User> => {
    return apiHelpers.get('/auth/me');
  },

  // Update user profile
  updateProfile: async (profileData: UpdateProfileRequest): Promise<User> => {
    return apiHelpers.put('/auth/profile', profileData);
  },

  // Change password
  changePassword: async (passwordData: ChangePasswordRequest): Promise<void> => {
    return apiHelpers.post('/auth/change-password', passwordData);
  },

  // Forgot password
  forgotPassword: async (data: ForgotPasswordRequest): Promise<{ message: string }> => {
    return apiHelpers.post('/auth/forgot-password', data);
  },

  // Reset password
  resetPassword: async (data: ResetPasswordRequest): Promise<{ message: string }> => {
    return apiHelpers.post('/auth/reset-password', data);
  },

  // Verify email
  verifyEmail: async (token: string): Promise<{ message: string }> => {
    return apiHelpers.post('/auth/verify-email', { token });
  },

  // Resend email verification
  resendEmailVerification: async (): Promise<{ message: string }> => {
    return apiHelpers.post('/auth/resend-verification');
  },

  // Enable two-factor authentication
  enableTwoFactor: async (): Promise<{
    qrCode: string;
    manualEntryKey: string;
    recoveryCodes: string[];
  }> => {
    return apiHelpers.post('/auth/enable-2fa');
  },

  // Verify two-factor authentication setup
  verifyTwoFactor: async (code: string): Promise<{
    recoveryCodes: string[];
    message: string;
  }> => {
    return apiHelpers.post('/auth/verify-2fa', { code });
  },

  // Disable two-factor authentication
  disableTwoFactor: async (password: string): Promise<{ message: string }> => {
    return apiHelpers.post('/auth/disable-2fa', { password });
  },

  // Generate new recovery codes
  generateRecoveryCodes: async (): Promise<{ recoveryCodes: string[] }> => {
    return apiHelpers.post('/auth/generate-recovery-codes');
  },

  // Upload profile picture
  uploadProfilePicture: async (file: File): Promise<{ profilePictureUrl: string }> => {
    return apiHelpers.upload('/auth/profile-picture', file);
  },

  // Delete profile picture
  deleteProfilePicture: async (): Promise<void> => {
    return apiHelpers.delete('/auth/profile-picture');
  },

  // Get user permissions
  getUserPermissions: async (): Promise<string[]> => {
    return apiHelpers.get('/auth/permissions');
  },

  // Get user roles
  getUserRoles: async (): Promise<string[]> => {
    return apiHelpers.get('/auth/roles');
  },

  // Check if user has permission
  hasPermission: async (permission: string): Promise<{ hasPermission: boolean }> => {
    return apiHelpers.get(`/auth/has-permission/${permission}`);
  },

  // Get user activity log
  getUserActivity: async (params?: {
    page?: number;
    pageSize?: number;
    startDate?: string;
    endDate?: string;
  }): Promise<{
    items: Array<{
      id: string;
      action: string;
      description: string;
      ipAddress: string;
      userAgent: string;
      timestamp: string;
    }>;
    totalCount: number;
    page: number;
    pageSize: number;
  }> => {
    return apiHelpers.get('/auth/activity', { params });
  },

  // Update user preferences
  updatePreferences: async (preferences: Record<string, any>): Promise<void> => {
    return apiHelpers.put('/auth/preferences', preferences);
  },

  // Get user preferences
  getPreferences: async (): Promise<Record<string, any>> => {
    return apiHelpers.get('/auth/preferences');
  },

  // Delete user account
  deleteAccount: async (password: string): Promise<{ message: string }> => {
    return apiHelpers.delete('/auth/account', {
      data: { password }
    });
  },

  // Export user data (GDPR compliance)
  exportUserData: async (): Promise<void> => {
    return apiHelpers.download('/auth/export-data', 'user-data.json');
  },
};
