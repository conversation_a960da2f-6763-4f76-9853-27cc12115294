using System.ComponentModel.DataAnnotations;

namespace SchoolManagement.Application.DTOs;

/// <summary>
/// DTO for creating a new tenant
/// </summary>
public class CreateTenantDto
{
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [MaxLength(50)]
    public string Code { get; set; } = string.Empty;

    [MaxLength(500)]
    public string? Description { get; set; }

    [Required]
    [EmailAddress]
    [MaxLength(100)]
    public string ContactEmail { get; set; } = string.Empty;

    [Phone]
    [MaxLength(20)]
    public string? ContactPhone { get; set; }

    [MaxLength(500)]
    public string? Address { get; set; }

    [MaxLength(100)]
    public string? City { get; set; }

    [MaxLength(100)]
    public string? State { get; set; }

    [MaxLength(20)]
    public string? PostalCode { get; set; }

    [MaxLength(100)]
    public string? Country { get; set; }

    public DateTime? SubscriptionStartDate { get; set; }

    public DateTime? SubscriptionEndDate { get; set; }

    [MaxLength(50)]
    public string? SubscriptionPlan { get; set; }

    [Range(1, 10000)]
    public int MaxStudents { get; set; } = 1000;

    [Range(1, 1000)]
    public int MaxStaff { get; set; } = 100;

    public Dictionary<string, object>? Settings { get; set; }

    public Dictionary<string, object>? BrandingConfig { get; set; }

    public Dictionary<string, object>? ComplianceConfig { get; set; }
}

/// <summary>
/// DTO for updating a tenant
/// </summary>
public class UpdateTenantDto
{
    [Required]
    public Guid Id { get; set; }

    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    [MaxLength(500)]
    public string? Description { get; set; }

    [Required]
    [EmailAddress]
    [MaxLength(100)]
    public string ContactEmail { get; set; } = string.Empty;

    [Phone]
    [MaxLength(20)]
    public string? ContactPhone { get; set; }

    [MaxLength(500)]
    public string? Address { get; set; }

    [MaxLength(100)]
    public string? City { get; set; }

    [MaxLength(100)]
    public string? State { get; set; }

    [MaxLength(20)]
    public string? PostalCode { get; set; }

    [MaxLength(100)]
    public string? Country { get; set; }

    public bool IsActive { get; set; } = true;

    public DateTime? SubscriptionStartDate { get; set; }

    public DateTime? SubscriptionEndDate { get; set; }

    [MaxLength(50)]
    public string? SubscriptionPlan { get; set; }

    [Range(1, 10000)]
    public int MaxStudents { get; set; } = 1000;

    [Range(1, 1000)]
    public int MaxStaff { get; set; } = 100;

    public Dictionary<string, object>? Settings { get; set; }

    public Dictionary<string, object>? BrandingConfig { get; set; }

    public Dictionary<string, object>? ComplianceConfig { get; set; }
}

/// <summary>
/// DTO for tenant response
/// </summary>
public class TenantDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string ContactEmail { get; set; } = string.Empty;
    public string? ContactPhone { get; set; }
    public string? Address { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? PostalCode { get; set; }
    public string? Country { get; set; }
    public bool IsActive { get; set; }
    public DateTime? SubscriptionStartDate { get; set; }
    public DateTime? SubscriptionEndDate { get; set; }
    public string? SubscriptionPlan { get; set; }
    public int MaxStudents { get; set; }
    public int MaxStaff { get; set; }
    public int CurrentStudents { get; set; }
    public int CurrentStaff { get; set; }
    public Dictionary<string, object>? Settings { get; set; }
    public Dictionary<string, object>? BrandingConfig { get; set; }
    public Dictionary<string, object>? ComplianceConfig { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// DTO for tenant summary (for listing)
/// </summary>
public class TenantSummaryDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
    public string? ContactPhone { get; set; }
    public bool IsActive { get; set; }
    public string? SubscriptionPlan { get; set; }
    public int MaxStudents { get; set; }
    public int CurrentStudents { get; set; }
    public int MaxStaff { get; set; }
    public int CurrentStaff { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// DTO for tenant configuration
/// </summary>
public class TenantConfigurationDto
{
    public Guid TenantId { get; set; }
    public Dictionary<string, object>? Settings { get; set; }
    public Dictionary<string, object>? BrandingConfig { get; set; }
    public Dictionary<string, object>? ComplianceConfig { get; set; }
}

/// <summary>
/// DTO for tenant branding configuration
/// </summary>
public class TenantBrandingDto
{
    public string? LogoUrl { get; set; }
    public string? PrimaryColor { get; set; }
    public string? SecondaryColor { get; set; }
    public string? AccentColor { get; set; }
    public string? FontFamily { get; set; }
    public string? Theme { get; set; }
    public Dictionary<string, string>? CustomCss { get; set; }
    public Dictionary<string, string>? CustomLabels { get; set; }
}

/// <summary>
/// DTO for tenant compliance configuration
/// </summary>
public class TenantComplianceDto
{
    public string? Country { get; set; }
    public string? TaxRegistrationNumber { get; set; }
    public bool EnableEInvoicing { get; set; }
    public bool EnableGDPRCompliance { get; set; }
    public bool EnableFERPACompliance { get; set; }
    public Dictionary<string, object>? RegionalSettings { get; set; }
    public Dictionary<string, object>? ComplianceRules { get; set; }
}
