using System.ComponentModel.DataAnnotations;

namespace SchoolManagement.Core.Entities;

/// <summary>
/// Represents a user in the system with multi-tenant support
/// </summary>
public class User : TenantEntity
{
    [Required]
    [MaxLength(256)]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string LastName { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? MiddleName { get; set; }
    
    [MaxLength(20)]
    public string? PhoneNumber { get; set; }
    
    public DateTime? DateOfBirth { get; set; }
    
    [MaxLength(10)]
    public string? Gender { get; set; }
    
    [MaxLength(500)]
    public string? Address { get; set; }
    
    [MaxLength(100)]
    public string? City { get; set; }
    
    [MaxLength(100)]
    public string? State { get; set; }
    
    [MaxLength(20)]
    public string? PostalCode { get; set; }
    
    [MaxLength(100)]
    public string? Country { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public DateTime? LastLoginAt { get; set; }
    
    [MaxLength(45)]
    public string? LastLoginIp { get; set; }
    
    public bool EmailConfirmed { get; set; } = false;
    
    public bool PhoneNumberConfirmed { get; set; } = false;
    
    public bool TwoFactorEnabled { get; set; } = false;
    
    public DateTime? LockoutEnd { get; set; }
    
    public bool LockoutEnabled { get; set; } = true;
    
    public int AccessFailedCount { get; set; } = 0;
    
    /// <summary>
    /// User profile picture URL
    /// </summary>
    public string? ProfilePictureUrl { get; set; }
    
    /// <summary>
    /// User preferences stored as JSON
    /// </summary>
    public string? Preferences { get; set; }
    
    /// <summary>
    /// External authentication provider ID (Azure AD, Google, etc.)
    /// </summary>
    public string? ExternalProviderId { get; set; }
    
    /// <summary>
    /// External authentication provider name
    /// </summary>
    [MaxLength(50)]
    public string? ExternalProvider { get; set; }
    
    // Navigation properties
    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
    public virtual ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();
    public virtual Student? Student { get; set; }
    public virtual Staff? Staff { get; set; }
    
    // Computed properties
    public string FullName => $"{FirstName} {MiddleName} {LastName}".Replace("  ", " ").Trim();
    public string DisplayName => $"{FirstName} {LastName}";
}

/// <summary>
/// User roles for role-based access control
/// </summary>
public class Role : TenantEntity
{
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    public bool IsSystemRole { get; set; } = false;
    
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
    public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
}

/// <summary>
/// Many-to-many relationship between users and roles
/// </summary>
public class UserRole : TenantEntity
{
    [Required]
    public Guid UserId { get; set; }
    
    [Required]
    public Guid RoleId { get; set; }
    
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;
    
    public string? AssignedBy { get; set; }
    
    // Navigation properties
    public virtual User User { get; set; } = null!;
    public virtual Role Role { get; set; } = null!;
}

/// <summary>
/// Permissions in the system
/// </summary>
public class Permission : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string Module { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(50)]
    public string Action { get; set; } = string.Empty; // CREATE, READ, UPDATE, DELETE
    
    public bool IsSystemPermission { get; set; } = false;
    
    // Navigation properties
    public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
    public virtual ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();
}

/// <summary>
/// Many-to-many relationship between roles and permissions
/// </summary>
public class RolePermission : TenantEntity
{
    [Required]
    public Guid RoleId { get; set; }
    
    [Required]
    public Guid PermissionId { get; set; }
    
    // Navigation properties
    public virtual Role Role { get; set; } = null!;
    public virtual Permission Permission { get; set; } = null!;
}

/// <summary>
/// Direct user permissions (overrides role permissions)
/// </summary>
public class UserPermission : TenantEntity
{
    [Required]
    public Guid UserId { get; set; }
    
    [Required]
    public Guid PermissionId { get; set; }
    
    public bool IsGranted { get; set; } = true;
    
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;
    
    public string? AssignedBy { get; set; }
    
    // Navigation properties
    public virtual User User { get; set; } = null!;
    public virtual Permission Permission { get; set; } = null!;
}
