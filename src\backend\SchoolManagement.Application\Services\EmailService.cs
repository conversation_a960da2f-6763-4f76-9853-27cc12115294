using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SchoolManagement.Application.Interfaces;
using System.Net;
using System.Net.Mail;

namespace SchoolManagement.Application.Services;

public class EmailService : IEmailService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<EmailService> _logger;

    public EmailService(IConfiguration configuration, ILogger<EmailService> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public async Task SendEmailAsync(string to, string subject, string body, bool isHtml = true)
    {
        try
        {
            var smtpServer = _configuration["Email:SmtpServer"];
            var smtpPort = int.Parse(_configuration["Email:SmtpPort"] ?? "587");
            var smtpUsername = _configuration["Email:SmtpUsername"];
            var smtpPassword = _configuration["Email:SmtpPassword"];
            var fromEmail = _configuration["Email:FromEmail"];
            var fromName = _configuration["Email:FromName"];

            if (string.IsNullOrEmpty(smtpServer) || string.IsNullOrEmpty(fromEmail))
            {
                _logger.LogWarning("Email configuration is missing. Email not sent.");
                return;
            }

            using var client = new SmtpClient(smtpServer, smtpPort);
            client.EnableSsl = true;
            client.UseDefaultCredentials = false;
            
            if (!string.IsNullOrEmpty(smtpUsername) && !string.IsNullOrEmpty(smtpPassword))
            {
                client.Credentials = new NetworkCredential(smtpUsername, smtpPassword);
            }

            var mailMessage = new MailMessage
            {
                From = new MailAddress(fromEmail, fromName),
                Subject = subject,
                Body = body,
                IsBodyHtml = isHtml
            };

            mailMessage.To.Add(to);

            await client.SendMailAsync(mailMessage);
            _logger.LogInformation("Email sent successfully to {Email}", to);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email to {Email}", to);
            throw;
        }
    }

    public async Task SendEmailVerificationAsync(string email, string firstName, string token)
    {
        var subject = "Verify Your Email Address";
        var body = $@"
            <h2>Welcome to School Management System, {firstName}!</h2>
            <p>Please verify your email address by clicking the link below:</p>
            <p><a href='#' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Verify Email</a></p>
            <p>If you didn't create this account, please ignore this email.</p>
            <p>Best regards,<br>School Management Team</p>
        ";

        await SendEmailAsync(email, subject, body, true);
    }

    public async Task SendPasswordResetAsync(string email, string firstName, string token)
    {
        var subject = "Reset Your Password";
        var body = $@"
            <h2>Password Reset Request</h2>
            <p>Hello {firstName},</p>
            <p>We received a request to reset your password. Click the link below to reset it:</p>
            <p><a href='#' style='background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Reset Password</a></p>
            <p>If you didn't request this, please ignore this email. Your password will remain unchanged.</p>
            <p>This link will expire in 24 hours.</p>
            <p>Best regards,<br>School Management Team</p>
        ";

        await SendEmailAsync(email, subject, body, true);
    }

    public async Task SendWelcomeEmailAsync(string email, string firstName, string temporaryPassword)
    {
        var subject = "Welcome to School Management System";
        var body = $@"
            <h2>Welcome to School Management System, {firstName}!</h2>
            <p>Your account has been created successfully. Here are your login credentials:</p>
            <p><strong>Email:</strong> {email}</p>
            <p><strong>Temporary Password:</strong> {temporaryPassword}</p>
            <p><strong>Important:</strong> Please change your password after your first login for security reasons.</p>
            <p><a href='#' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Login Now</a></p>
            <p>Best regards,<br>School Management Team</p>
        ";

        await SendEmailAsync(email, subject, body, true);
    }

    public async Task SendNotificationEmailAsync(string email, string subject, string message)
    {
        var body = $@"
            <h2>{subject}</h2>
            <p>{message}</p>
            <p>Best regards,<br>School Management Team</p>
        ";

        await SendEmailAsync(email, subject, body, true);
    }

    public async Task SendBulkEmailAsync(IEnumerable<string> recipients, string subject, string body, bool isHtml = true)
    {
        var tasks = recipients.Select(email => SendEmailAsync(email, subject, body, isHtml));
        await Task.WhenAll(tasks);
    }
}
