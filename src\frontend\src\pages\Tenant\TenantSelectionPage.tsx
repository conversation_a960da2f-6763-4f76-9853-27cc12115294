import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  TextField,
  Button,
  Typography,
  Container,
  Alert,
  useTheme,
  alpha,
} from '@mui/material';
import { Business, Search } from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useTenant } from '../../hooks/useTenant';
import { useNavigate } from 'react-router-dom';

const validationSchema = Yup.object({
  tenantCode: Yup.string()
    .required('School code is required')
    .min(2, 'School code must be at least 2 characters'),
});

const TenantSelectionPage: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { loadTenantByCode, isLoading, error } = useTenant();
  const [localError, setLocalError] = useState<string | null>(null);

  const formik = useFormik({
    initialValues: {
      tenantCode: '',
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        setLocalError(null);
        await loadTenantByCode(values.tenantCode);
        navigate('/login');
      } catch (error: any) {
        setLocalError(error.message || 'Failed to find school. Please check the school code.');
      }
    },
  });

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        py: 3,
      }}
    >
      <Container maxWidth="sm">
        <Card
          elevation={8}
          sx={{
            borderRadius: 3,
            overflow: 'hidden',
            boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
          }}
        >
          <CardContent sx={{ p: 4 }}>
            {/* Header */}
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Box
                sx={{
                  width: 64,
                  height: 64,
                  borderRadius: 2,
                  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '1.5rem',
                  fontWeight: 700,
                  mx: 'auto',
                  mb: 2,
                }}
              >
                SMS
              </Box>
              
              <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                Find Your School
              </Typography>
              
              <Typography variant="body1" color="text.secondary">
                Enter your school code to access the management system
              </Typography>
            </Box>

            {/* Error Alert */}
            {(error || localError) && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error || localError}
              </Alert>
            )}

            {/* Search Form */}
            <form onSubmit={formik.handleSubmit}>
              <TextField
                fullWidth
                id="tenantCode"
                name="tenantCode"
                label="School Code"
                placeholder="Enter your school code (e.g., ABC123)"
                value={formik.values.tenantCode}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.tenantCode && Boolean(formik.errors.tenantCode)}
                helperText={formik.touched.tenantCode && formik.errors.tenantCode}
                InputProps={{
                  startAdornment: (
                    <Business sx={{ color: 'action.active', mr: 1 }} />
                  ),
                }}
                sx={{ mb: 3 }}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={isLoading}
                startIcon={<Search />}
                sx={{
                  py: 1.5,
                  fontSize: '1rem',
                  fontWeight: 600,
                  borderRadius: 2,
                  textTransform: 'none',
                  mb: 3,
                }}
              >
                {isLoading ? 'Searching...' : 'Find School'}
              </Button>
            </form>

            {/* Help Section */}
            <Box sx={{ textAlign: 'center', pt: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Don't know your school code?
              </Typography>
              
              <Button
                variant="text"
                sx={{
                  color: 'primary.main',
                  textDecoration: 'none',
                  fontWeight: 500,
                  textTransform: 'none',
                  '&:hover': {
                    textDecoration: 'underline',
                    backgroundColor: 'transparent',
                  },
                }}
              >
                Contact your school administrator
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* Footer */}
        <Box sx={{ textAlign: 'center', mt: 3 }}>
          <Typography variant="body2" color="text.secondary">
            © 2024 School Management System. All rights reserved.
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default TenantSelectionPage;
