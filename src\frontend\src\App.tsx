import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box } from '@mui/material';
import { Helmet } from 'react-helmet-async';

import { useAuth } from './hooks/useAuth';
import { useTenant } from './hooks/useTenant';
import Layout from './components/Layout/Layout';
import LoadingScreen from './components/Common/LoadingScreen';
import ErrorBoundary from './components/Common/ErrorBoundary';

// Pages
import LoginPage from './pages/Auth/LoginPage';
import DashboardPage from './pages/Dashboard/DashboardPage';
import StudentsPage from './pages/Students/StudentsPage';
import StaffPage from './pages/Staff/StaffPage';
import AcademicsPage from './pages/Academics/AcademicsPage';
import FinancePage from './pages/Finance/FinancePage';
import AttendancePage from './pages/Attendance/AttendancePage';
import ReportsPage from './pages/Reports/ReportsPage';
import SettingsPage from './pages/Settings/SettingsPage';
import TenantSelectionPage from './pages/Tenant/TenantSelectionPage';
import NotFoundPage from './pages/Common/NotFoundPage';

// Protected Route Component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const { currentTenant, isLoading: tenantLoading } = useTenant();

  if (isLoading || tenantLoading) {
    return <LoadingScreen />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (!currentTenant) {
    return <Navigate to="/tenant-selection" replace />;
  }

  return <>{children}</>;
};

// Public Route Component (redirects to dashboard if authenticated)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const { currentTenant } = useTenant();

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (isAuthenticated && currentTenant) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

const App: React.FC = () => {
  const { currentTenant } = useTenant();

  useEffect(() => {
    // Hide loading screen when app is ready
    document.body.classList.add('app-loaded');
  }, []);

  return (
    <ErrorBoundary>
      <Helmet>
        <title>
          {currentTenant ? `${currentTenant.name} - School Management` : 'School Management System'}
        </title>
        <meta name="description" content="Comprehensive school management system with multi-tenant support" />
      </Helmet>

      <Box sx={{ display: 'flex', minHeight: '100vh' }}>
        <Routes>
          {/* Public Routes */}
          <Route
            path="/login"
            element={
              <PublicRoute>
                <LoginPage />
              </PublicRoute>
            }
          />
          
          <Route
            path="/tenant-selection"
            element={
              <PublicRoute>
                <TenantSelectionPage />
              </PublicRoute>
            }
          />

          {/* Protected Routes */}
          <Route
            path="/*"
            element={
              <ProtectedRoute>
                <Layout>
                  <Routes>
                    <Route path="/" element={<Navigate to="/dashboard" replace />} />
                    <Route path="/dashboard" element={<DashboardPage />} />
                    
                    {/* Student Management */}
                    <Route path="/students/*" element={<StudentsPage />} />
                    
                    {/* Staff Management */}
                    <Route path="/staff/*" element={<StaffPage />} />
                    
                    {/* Academic Management */}
                    <Route path="/academics/*" element={<AcademicsPage />} />
                    
                    {/* Financial Management */}
                    <Route path="/finance/*" element={<FinancePage />} />
                    
                    {/* Attendance Management */}
                    <Route path="/attendance/*" element={<AttendancePage />} />
                    
                    {/* Reports */}
                    <Route path="/reports/*" element={<ReportsPage />} />
                    
                    {/* Settings */}
                    <Route path="/settings/*" element={<SettingsPage />} />
                    
                    {/* 404 Page */}
                    <Route path="*" element={<NotFoundPage />} />
                  </Routes>
                </Layout>
              </ProtectedRoute>
            }
          />
        </Routes>
      </Box>
    </ErrorBoundary>
  );
};

export default App;
