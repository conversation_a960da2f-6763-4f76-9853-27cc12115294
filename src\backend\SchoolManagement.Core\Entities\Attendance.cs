using System.ComponentModel.DataAnnotations;

namespace SchoolManagement.Core.Entities;

/// <summary>
/// Student attendance tracking
/// </summary>
public class StudentAttendance : TenantEntity
{
    [Required]
    public Guid StudentId { get; set; }
    
    [Required]
    public DateTime Date { get; set; }
    
    public Guid? SubjectId { get; set; }
    
    public Guid? ClassScheduleId { get; set; }
    
    [Required]
    public AttendanceStatus Status { get; set; } = AttendanceStatus.Present;
    
    public TimeSpan? CheckInTime { get; set; }
    
    public TimeSpan? CheckOutTime { get; set; }
    
    public bool IsLate { get; set; } = false;
    
    public TimeSpan? LateBy { get; set; }
    
    public bool IsEarlyLeave { get; set; } = false;
    
    public TimeSpan? EarlyLeaveBy { get; set; }
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    [MaxLength(500)]
    public string? Reason { get; set; }
    
    public string? MarkedBy { get; set; }
    
    public DateTime? MarkedAt { get; set; }
    
    /// <summary>
    /// Attendance method (Manual, Biometric, Card, etc.)
    /// </summary>
    [MaxLength(50)]
    public string? AttendanceMethod { get; set; }
    
    /// <summary>
    /// Device or location where attendance was marked
    /// </summary>
    [MaxLength(100)]
    public string? AttendanceLocation { get; set; }
    
    // Navigation properties
    public virtual Student Student { get; set; } = null!;
    public virtual Subject? Subject { get; set; }
    public virtual ClassSchedule? ClassSchedule { get; set; }
}

/// <summary>
/// Attendance summary for reporting
/// </summary>
public class AttendanceSummary : TenantEntity
{
    [Required]
    public Guid StudentId { get; set; }
    
    [Required]
    public DateTime PeriodStart { get; set; }
    
    [Required]
    public DateTime PeriodEnd { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string PeriodType { get; set; } = string.Empty; // Daily, Weekly, Monthly, Term
    
    public int TotalDays { get; set; }
    
    public int PresentDays { get; set; }
    
    public int AbsentDays { get; set; }
    
    public int LateDays { get; set; }
    
    public int HalfDays { get; set; }
    
    public int LeaveDays { get; set; }
    
    public decimal AttendancePercentage => TotalDays > 0 ? (decimal)PresentDays / TotalDays * 100 : 0;
    
    public TimeSpan? TotalLateTime { get; set; }
    
    public TimeSpan? AverageLateTime { get; set; }
    
    // Navigation properties
    public virtual Student Student { get; set; } = null!;
}

/// <summary>
/// Attendance rules and policies
/// </summary>
public class AttendancePolicy : TenantEntity
{
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    public Guid? GradeId { get; set; }
    
    /// <summary>
    /// Minimum attendance percentage required
    /// </summary>
    public decimal MinimumAttendancePercentage { get; set; } = 75;
    
    /// <summary>
    /// Grace period for late arrival (in minutes)
    /// </summary>
    public int LateGracePeriodMinutes { get; set; } = 15;
    
    /// <summary>
    /// Maximum allowed late days per term
    /// </summary>
    public int MaxLateDaysPerTerm { get; set; } = 5;
    
    /// <summary>
    /// Maximum allowed absent days per term
    /// </summary>
    public int MaxAbsentDaysPerTerm { get; set; } = 10;
    
    /// <summary>
    /// Auto-mark absent after this time (in minutes from class start)
    /// </summary>
    public int AutoMarkAbsentAfterMinutes { get; set; } = 30;
    
    /// <summary>
    /// Send notification to parents after consecutive absent days
    /// </summary>
    public int NotifyParentsAfterAbsentDays { get; set; } = 3;
    
    /// <summary>
    /// Require medical certificate for absence longer than this (days)
    /// </summary>
    public int RequireMedicalCertificateAfterDays { get; set; } = 3;
    
    public bool IsActive { get; set; } = true;
    
    /// <summary>
    /// Policy configuration stored as JSON
    /// </summary>
    public string? PolicyConfig { get; set; }
    
    // Navigation properties
    public virtual Grade? Grade { get; set; }
}

/// <summary>
/// Leave requests from students/parents
/// </summary>
public class StudentLeave : TenantEntity
{
    [Required]
    public Guid StudentId { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string LeaveType { get; set; } = string.Empty; // Sick, Personal, Emergency, etc.
    
    [Required]
    public DateTime StartDate { get; set; }
    
    [Required]
    public DateTime EndDate { get; set; }
    
    public int TotalDays { get; set; }
    
    [Required]
    [MaxLength(500)]
    public string Reason { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Comments { get; set; }
    
    public LeaveStatus Status { get; set; } = LeaveStatus.Pending;
    
    public DateTime? ApprovedAt { get; set; }
    
    public string? ApprovedBy { get; set; }
    
    [MaxLength(500)]
    public string? ApprovalComments { get; set; }
    
    public string? RequestedBy { get; set; } // Parent/Guardian who requested
    
    /// <summary>
    /// Supporting documents (medical certificate, etc.)
    /// </summary>
    public string? SupportingDocuments { get; set; }
    
    // Navigation properties
    public virtual Student Student { get; set; } = null!;
}

/// <summary>
/// Attendance notifications
/// </summary>
public class AttendanceNotification : TenantEntity
{
    [Required]
    public Guid StudentId { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string NotificationType { get; set; } = string.Empty; // Absent, Late, LowAttendance
    
    [Required]
    [MaxLength(500)]
    public string Message { get; set; } = string.Empty;
    
    [Required]
    public DateTime NotificationDate { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string Channel { get; set; } = string.Empty; // SMS, Email, Push, Portal
    
    [MaxLength(100)]
    public string? Recipient { get; set; } // Parent/Guardian email or phone
    
    public bool IsSent { get; set; } = false;
    
    public DateTime? SentAt { get; set; }
    
    public bool IsRead { get; set; } = false;
    
    public DateTime? ReadAt { get; set; }
    
    [MaxLength(500)]
    public string? Response { get; set; }
    
    /// <summary>
    /// Notification metadata stored as JSON
    /// </summary>
    public string? Metadata { get; set; }
    
    // Navigation properties
    public virtual Student Student { get; set; } = null!;
}

/// <summary>
/// Attendance device integration
/// </summary>
public class AttendanceDevice : TenantEntity
{
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(50)]
    public string DeviceType { get; set; } = string.Empty; // Biometric, Card Reader, Mobile App
    
    [Required]
    [MaxLength(100)]
    public string DeviceId { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? Location { get; set; }
    
    [MaxLength(45)]
    public string? IpAddress { get; set; }
    
    [MaxLength(10)]
    public string? Port { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public DateTime? LastSyncAt { get; set; }
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    /// <summary>
    /// Device configuration stored as JSON
    /// </summary>
    public string? DeviceConfig { get; set; }
    
    // Navigation properties
    public virtual ICollection<AttendanceDeviceLog> DeviceLogs { get; set; } = new List<AttendanceDeviceLog>();
}

/// <summary>
/// Attendance device logs
/// </summary>
public class AttendanceDeviceLog : TenantEntity
{
    [Required]
    public Guid AttendanceDeviceId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string UserId { get; set; } = string.Empty; // Student/Staff ID
    
    [Required]
    public DateTime Timestamp { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string EventType { get; set; } = string.Empty; // CheckIn, CheckOut
    
    public bool IsProcessed { get; set; } = false;
    
    public DateTime? ProcessedAt { get; set; }
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    /// <summary>
    /// Raw device data stored as JSON
    /// </summary>
    public string? RawData { get; set; }
    
    // Navigation properties
    public virtual AttendanceDevice AttendanceDevice { get; set; } = null!;
}
