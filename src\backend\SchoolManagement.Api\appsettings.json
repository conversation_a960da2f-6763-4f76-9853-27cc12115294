{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=SchoolManagementDb;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"}, "Jwt": {"Key": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "SchoolManagementAPI", "Audience": "SchoolManagementClients", "ExpiryInMinutes": 60}, "AllowedOrigins": ["http://localhost:3000", "https://localhost:3000", "http://localhost:3001", "https://localhost:3001"], "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/schoolmanagement-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext"]}, "Azure": {"Storage": {"ConnectionString": "", "ContainerName": "school-documents"}, "ServiceBus": {"ConnectionString": ""}}, "Redis": {"ConnectionString": "localhost:6379"}, "Email": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SmtpUsername": "", "SmtpPassword": "", "FromEmail": "<EMAIL>", "FromName": "School Management System"}, "SMS": {"Provider": "<PERSON><PERSON><PERSON>", "AccountSid": "", "AuthToken": "", "FromNumber": ""}, "Features": {"EnableMultiTenancy": true, "EnableAuditLogging": true, "EnableFileStorage": true, "EnableNotifications": true, "EnableEInvoicing": true, "EnableGDPRCompliance": true, "EnableFERPACompliance": true}, "Security": {"RequireHttps": true, "EnableRateLimiting": true, "MaxRequestsPerMinute": 100, "EnableCors": true, "AllowedFileTypes": [".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png", ".gif"], "MaxFileSize": ********}, "Compliance": {"Egypt": {"TaxAuthority": {"BaseUrl": "https://api.eta.gov.eg", "ClientId": "", "ClientSecret": ""}}, "Saudi": {"ZATCA": {"BaseUrl": "https://gw-fatoora.zatca.gov.sa", "CertificatePath": "", "PrivateKeyPath": ""}}}}