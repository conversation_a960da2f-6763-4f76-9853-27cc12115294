import React, { useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box, Typography } from '@mui/material';
import { useAppDispatch } from '../../store/store';
import { setPageTitle, setBreadcrumbs } from '../../store/slices/uiSlice';

const AttendanceListPage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setPageTitle('Attendance'));
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', path: '/dashboard' },
      { label: 'Attendance' }
    ]));
  }, [dispatch]);

  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 700, mb: 3 }}>
        Attendance Management
      </Typography>
      <Typography variant="body1" color="text.secondary">
        Attendance management functionality will be implemented here.
      </Typography>
    </Box>
  );
};

const AttendancePage: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<AttendanceListPage />} />
      <Route path="/daily" element={<AttendanceListPage />} />
      <Route path="/calendar" element={<AttendanceListPage />} />
      <Route path="/reports" element={<AttendanceListPage />} />
    </Routes>
  );
};

export default AttendancePage;
