import { apiHelpers } from './apiClient';
import { StudentAttendance, AttendanceSummary, StudentLeave } from '../../store/slices/attendanceSlice';

export const attendanceApi = {
  // Student Attendance
  getStudentAttendance: async (
    studentId: string,
    startDate: string,
    endDate: string
  ): Promise<StudentAttendance[]> => {
    return apiHelpers.get('/attendance/student', {
      params: { studentId, startDate, endDate }
    });
  },

  getClassAttendance: async (gradeId: string, date: string): Promise<StudentAttendance[]> => {
    return apiHelpers.get('/attendance/class', {
      params: { gradeId, date }
    });
  },

  markAttendance: async (
    studentId: string,
    date: string,
    status: string,
    notes?: string
  ): Promise<StudentAttendance> => {
    return apiHelpers.post('/attendance/mark', {
      studentId,
      date,
      status,
      notes
    });
  },

  markBulkAttendance: async (data: {
    gradeId: string;
    date: string;
    attendances: Array<{
      studentId: string;
      status: string;
      notes?: string;
    }>;
  }): Promise<StudentAttendance[]> => {
    return apiHelpers.post('/attendance/mark-bulk', data);
  },

  updateAttendance: async (id: string, data: {
    status: string;
    checkInTime?: string;
    checkOutTime?: string;
    notes?: string;
    reason?: string;
  }): Promise<StudentAttendance> => {
    return apiHelpers.put(`/attendance/${id}`, data);
  },

  deleteAttendance: async (id: string): Promise<void> => {
    return apiHelpers.delete(`/attendance/${id}`);
  },

  // Attendance Summary
  getAttendanceSummary: async (
    studentId: string,
    periodStart: string,
    periodEnd: string
  ): Promise<AttendanceSummary> => {
    return apiHelpers.get('/attendance/summary', {
      params: { studentId, periodStart, periodEnd }
    });
  },

  getClassAttendanceSummary: async (
    gradeId: string,
    periodStart: string,
    periodEnd: string
  ): Promise<Array<{
    studentId: string;
    studentName: string;
    totalDays: number;
    presentDays: number;
    absentDays: number;
    lateDays: number;
    attendancePercentage: number;
  }>> => {
    return apiHelpers.get('/attendance/class-summary', {
      params: { gradeId, periodStart, periodEnd }
    });
  },

  getAttendanceStatistics: async (period?: string): Promise<{
    totalStudents: number;
    presentToday: number;
    absentToday: number;
    lateToday: number;
    averageAttendanceRate: number;
    monthlyTrends: Array<{
      month: string;
      attendanceRate: number;
      totalDays: number;
    }>;
    gradeWiseAttendance: Array<{
      gradeName: string;
      attendanceRate: number;
      totalStudents: number;
    }>;
  }> => {
    return apiHelpers.get('/attendance/statistics', { params: { period } });
  },

  // Leave Management
  getStudentLeaves: async (studentId: string): Promise<StudentLeave[]> => {
    return apiHelpers.get(`/attendance/leaves/student/${studentId}`);
  },

  getAllLeaves: async (params?: {
    status?: string;
    startDate?: string;
    endDate?: string;
    gradeId?: string;
  }): Promise<StudentLeave[]> => {
    return apiHelpers.get('/attendance/leaves', { params });
  },

  requestLeave: async (leaveData: {
    studentId: string;
    leaveType: string;
    startDate: string;
    endDate: string;
    reason: string;
    comments?: string;
    requestedBy?: string;
  }): Promise<StudentLeave> => {
    return apiHelpers.post('/attendance/leaves', leaveData);
  },

  updateLeave: async (id: string, data: Partial<StudentLeave>): Promise<StudentLeave> => {
    return apiHelpers.put(`/attendance/leaves/${id}`, data);
  },

  approveLeave: async (
    leaveId: string,
    approvedBy: string,
    comments?: string
  ): Promise<StudentLeave> => {
    return apiHelpers.post(`/attendance/leaves/${leaveId}/approve`, {
      approvedBy,
      comments
    });
  },

  rejectLeave: async (
    leaveId: string,
    rejectedBy: string,
    comments: string
  ): Promise<StudentLeave> => {
    return apiHelpers.post(`/attendance/leaves/${leaveId}/reject`, {
      rejectedBy,
      comments
    });
  },

  cancelLeave: async (leaveId: string, reason: string): Promise<void> => {
    return apiHelpers.post(`/attendance/leaves/${leaveId}/cancel`, { reason });
  },

  uploadLeaveDocument: async (leaveId: string, file: File): Promise<{ documentPath: string }> => {
    return apiHelpers.upload(`/attendance/leaves/${leaveId}/document`, file);
  },

  // Attendance Policies
  getAttendancePolicies: async (): Promise<Array<{
    id: string;
    name: string;
    description?: string;
    gradeId?: string;
    gradeName?: string;
    minimumAttendancePercentage: number;
    lateGracePeriodMinutes: number;
    maxLateDaysPerTerm: number;
    maxAbsentDaysPerTerm: number;
    autoMarkAbsentAfterMinutes: number;
    notifyParentsAfterAbsentDays: number;
    requireMedicalCertificateAfterDays: number;
    isActive: boolean;
  }>> => {
    return apiHelpers.get('/attendance/policies');
  },

  createAttendancePolicy: async (data: {
    name: string;
    description?: string;
    gradeId?: string;
    minimumAttendancePercentage: number;
    lateGracePeriodMinutes: number;
    maxLateDaysPerTerm: number;
    maxAbsentDaysPerTerm: number;
    autoMarkAbsentAfterMinutes: number;
    notifyParentsAfterAbsentDays: number;
    requireMedicalCertificateAfterDays: number;
  }): Promise<{
    id: string;
    message: string;
  }> => {
    return apiHelpers.post('/attendance/policies', data);
  },

  updateAttendancePolicy: async (id: string, data: any): Promise<void> => {
    return apiHelpers.put(`/attendance/policies/${id}`, data);
  },

  deleteAttendancePolicy: async (id: string): Promise<void> => {
    return apiHelpers.delete(`/attendance/policies/${id}`);
  },

  // Attendance Notifications
  getAttendanceNotifications: async (params?: {
    studentId?: string;
    type?: string;
    isRead?: boolean;
  }): Promise<Array<{
    id: string;
    studentId: string;
    studentName: string;
    notificationType: string;
    message: string;
    notificationDate: string;
    channel: string;
    recipient?: string;
    isSent: boolean;
    sentAt?: string;
    isRead: boolean;
    readAt?: string;
    response?: string;
  }>> => {
    return apiHelpers.get('/attendance/notifications', { params });
  },

  sendAttendanceNotification: async (
    studentId: string,
    notificationType: string,
    message: string,
    channel: 'SMS' | 'Email' | 'Push' | 'Portal' = 'Email'
  ): Promise<{
    id: string;
    message: string;
  }> => {
    return apiHelpers.post('/attendance/notifications', {
      studentId,
      notificationType,
      message,
      channel
    });
  },

  markNotificationAsRead: async (id: string): Promise<void> => {
    return apiHelpers.post(`/attendance/notifications/${id}/read`);
  },

  // Attendance Reports
  generateAttendanceReport: async (params: {
    reportType: 'daily' | 'weekly' | 'monthly' | 'term' | 'annual';
    startDate: string;
    endDate: string;
    gradeId?: string;
    format: 'pdf' | 'excel' | 'csv';
  }): Promise<void> => {
    const { reportType, format, ...otherParams } = params;
    return apiHelpers.download(
      `/attendance/reports/${reportType}`,
      `attendance-report-${reportType}.${format}`,
      { params: { ...otherParams, format } }
    );
  },

  getAttendanceAnalytics: async (params?: {
    period?: string;
    gradeId?: string;
  }): Promise<{
    overallAttendanceRate: number;
    totalStudents: number;
    chronicAbsentees: number;
    perfectAttendance: number;
    dailyTrends: Array<{
      date: string;
      attendanceRate: number;
      totalStudents: number;
      presentStudents: number;
    }>;
    gradeComparison: Array<{
      gradeName: string;
      attendanceRate: number;
      totalStudents: number;
    }>;
    attendancePatterns: Array<{
      dayOfWeek: string;
      averageAttendanceRate: number;
    }>;
    lateArrivalTrends: Array<{
      week: string;
      lateCount: number;
      averageLateTime: string;
    }>;
  }> => {
    return apiHelpers.get('/attendance/analytics', { params });
  },

  // Attendance Devices Integration
  getAttendanceDevices: async (): Promise<Array<{
    id: string;
    name: string;
    deviceType: string;
    deviceId: string;
    location?: string;
    ipAddress?: string;
    port?: string;
    isActive: boolean;
    lastSyncAt?: string;
  }>> => {
    return apiHelpers.get('/attendance/devices');
  },

  syncAttendanceDevice: async (deviceId: string): Promise<{
    message: string;
    syncedRecords: number;
    lastSyncAt: string;
  }> => {
    return apiHelpers.post(`/attendance/devices/${deviceId}/sync`);
  },

  getDeviceLogs: async (deviceId: string, params?: {
    startDate?: string;
    endDate?: string;
    isProcessed?: boolean;
  }): Promise<Array<{
    id: string;
    userId: string;
    timestamp: string;
    eventType: string;
    isProcessed: boolean;
    processedAt?: string;
    notes?: string;
  }>> => {
    return apiHelpers.get(`/attendance/devices/${deviceId}/logs`, { params });
  },

  // Bulk Operations
  bulkMarkAttendance: async (data: {
    date: string;
    attendances: Array<{
      studentId: string;
      status: string;
      notes?: string;
    }>;
  }): Promise<{
    processedCount: number;
    errors?: Array<{
      studentId: string;
      message: string;
    }>;
  }> => {
    return apiHelpers.post('/attendance/bulk-mark', data);
  },

  importAttendanceData: async (file: File): Promise<{
    message: string;
    importedCount: number;
    errors?: Array<{
      row: number;
      field: string;
      message: string;
    }>;
  }> => {
    return apiHelpers.upload('/attendance/import', file);
  },

  exportAttendanceData: async (params: {
    startDate: string;
    endDate: string;
    gradeId?: string;
    format: 'csv' | 'excel';
  }): Promise<void> => {
    const { format, ...otherParams } = params;
    return apiHelpers.download(
      '/attendance/export',
      `attendance-data.${format}`,
      { params: { ...otherParams, format } }
    );
  },
};
