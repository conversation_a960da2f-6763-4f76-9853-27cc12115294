using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SchoolManagement.Application.DTOs.Common;
using SchoolManagement.Application.Interfaces;
using SchoolManagement.Core.Entities.Base;
using SchoolManagement.Core.Interfaces;
using System.Linq.Expressions;

namespace SchoolManagement.Application.Services;

public abstract class CrudService<TEntity, TDto, TCreateDto, TUpdateDto> : ICrudService<TEntity, TDto, TCreateDto, TUpdateDto>
    where TEntity : class, IEntity
    where TDto : class
    where TCreateDto : class
    where TUpdateDto : class
{
    protected readonly IRepository<TEntity> Repository;
    protected readonly ILogger Logger;
    protected readonly ITenantContext TenantContext;

    protected CrudService(
        IRepository<TEntity> repository,
        ILogger logger,
        ITenantContext tenantContext)
    {
        Repository = repository;
        Logger = logger;
        TenantContext = tenantContext;
    }

    public virtual async Task<PagedResultDto<TDto>> GetAllAsync(
        int page = 1,
        int pageSize = 20,
        string? search = null,
        string? sortBy = null,
        string? sortOrder = "asc",
        Expression<Func<TEntity, bool>>? filter = null)
    {
        try
        {
            var query = Repository.GetQueryable();

            // Apply tenant filter if entity supports multi-tenancy
            query = ApplyTenantFilter(query);

            // Apply additional filters
            if (filter != null)
            {
                query = query.Where(filter);
            }

            // Apply search
            if (!string.IsNullOrEmpty(search))
            {
                query = ApplySearch(query, search);
            }

            // Get total count before pagination
            var totalCount = await query.CountAsync();

            // Apply sorting
            if (!string.IsNullOrEmpty(sortBy))
            {
                query = ApplySorting(query, sortBy, sortOrder);
            }
            else
            {
                query = ApplyDefaultSorting(query);
            }

            // Apply pagination
            var items = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var dtos = items.Select(MapToDto).ToList();

            return new PagedResultDto<TDto>(dtos, totalCount, page, pageSize);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error getting {EntityType} list", typeof(TEntity).Name);
            throw;
        }
    }

    public virtual async Task<TDto?> GetByIdAsync(Guid id)
    {
        try
        {
            var query = Repository.GetQueryable();
            query = ApplyTenantFilter(query);
            query = ApplyIncludes(query);

            var entity = await query.FirstOrDefaultAsync(e => e.Id == id);
            return entity != null ? MapToDto(entity) : null;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error getting {EntityType} by id: {Id}", typeof(TEntity).Name, id);
            throw;
        }
    }

    public virtual async Task<TDto> CreateAsync(TCreateDto createDto)
    {
        try
        {
            var entity = MapFromCreateDto(createDto);
            
            // Set tenant if entity supports multi-tenancy
            SetTenantId(entity);
            
            // Set audit fields
            SetCreateAuditFields(entity);

            await Repository.AddAsync(entity);
            await Repository.SaveChangesAsync();

            Logger.LogInformation("Created {EntityType} with id: {Id}", typeof(TEntity).Name, entity.Id);

            return MapToDto(entity);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    public virtual async Task<TDto> UpdateAsync(Guid id, TUpdateDto updateDto)
    {
        try
        {
            var query = Repository.GetQueryable();
            query = ApplyTenantFilter(query);

            var entity = await query.FirstOrDefaultAsync(e => e.Id == id);
            if (entity == null)
            {
                throw new KeyNotFoundException($"{typeof(TEntity).Name} with id {id} not found");
            }

            MapFromUpdateDto(updateDto, entity);
            
            // Set audit fields
            SetUpdateAuditFields(entity);

            Repository.Update(entity);
            await Repository.SaveChangesAsync();

            Logger.LogInformation("Updated {EntityType} with id: {Id}", typeof(TEntity).Name, id);

            return MapToDto(entity);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error updating {EntityType} with id: {Id}", typeof(TEntity).Name, id);
            throw;
        }
    }

    public virtual async Task DeleteAsync(Guid id)
    {
        try
        {
            var query = Repository.GetQueryable();
            query = ApplyTenantFilter(query);

            var entity = await query.FirstOrDefaultAsync(e => e.Id == id);
            if (entity == null)
            {
                throw new KeyNotFoundException($"{typeof(TEntity).Name} with id {id} not found");
            }

            // Check if entity supports soft delete
            if (entity is ISoftDelete softDeleteEntity)
            {
                softDeleteEntity.IsDeleted = true;
                softDeleteEntity.DeletedAt = DateTime.UtcNow;
                SetUpdateAuditFields(entity);
                Repository.Update(entity);
            }
            else
            {
                Repository.Delete(entity);
            }

            await Repository.SaveChangesAsync();

            Logger.LogInformation("Deleted {EntityType} with id: {Id}", typeof(TEntity).Name, id);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting {EntityType} with id: {Id}", typeof(TEntity).Name, id);
            throw;
        }
    }

    public virtual async Task<bool> ExistsAsync(Guid id)
    {
        try
        {
            var query = Repository.GetQueryable();
            query = ApplyTenantFilter(query);

            return await query.AnyAsync(e => e.Id == id);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error checking existence of {EntityType} with id: {Id}", typeof(TEntity).Name, id);
            throw;
        }
    }

    public virtual async Task<int> CountAsync(Expression<Func<TEntity, bool>>? filter = null)
    {
        try
        {
            var query = Repository.GetQueryable();
            query = ApplyTenantFilter(query);

            if (filter != null)
            {
                query = query.Where(filter);
            }

            return await query.CountAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error counting {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    // Abstract methods to be implemented by derived classes
    protected abstract TDto MapToDto(TEntity entity);
    protected abstract TEntity MapFromCreateDto(TCreateDto createDto);
    protected abstract void MapFromUpdateDto(TUpdateDto updateDto, TEntity entity);

    // Virtual methods that can be overridden by derived classes
    protected virtual IQueryable<TEntity> ApplyIncludes(IQueryable<TEntity> query)
    {
        return query;
    }

    protected virtual IQueryable<TEntity> ApplySearch(IQueryable<TEntity> query, string search)
    {
        return query;
    }

    protected virtual IQueryable<TEntity> ApplySorting(IQueryable<TEntity> query, string sortBy, string? sortOrder)
    {
        return query;
    }

    protected virtual IQueryable<TEntity> ApplyDefaultSorting(IQueryable<TEntity> query)
    {
        return query.OrderBy(e => e.CreatedAt);
    }

    protected virtual IQueryable<TEntity> ApplyTenantFilter(IQueryable<TEntity> query)
    {
        if (TenantContext.HasTenant && typeof(TEntity).IsAssignableTo(typeof(ITenantEntity)))
        {
            return query.Where(e => ((ITenantEntity)e).TenantId == TenantContext.TenantId);
        }
        return query;
    }

    protected virtual void SetTenantId(TEntity entity)
    {
        if (TenantContext.HasTenant && entity is ITenantEntity tenantEntity)
        {
            tenantEntity.TenantId = TenantContext.TenantId!.Value;
        }
    }

    protected virtual void SetCreateAuditFields(TEntity entity)
    {
        if (entity is IAuditableEntity auditableEntity)
        {
            auditableEntity.CreatedAt = DateTime.UtcNow;
            // Set CreatedBy if current user service is available
        }
    }

    protected virtual void SetUpdateAuditFields(TEntity entity)
    {
        if (entity is IAuditableEntity auditableEntity)
        {
            auditableEntity.UpdatedAt = DateTime.UtcNow;
            // Set UpdatedBy if current user service is available
        }
    }

    // Bulk operations
    public virtual async Task<IEnumerable<TDto>> CreateBulkAsync(IEnumerable<TCreateDto> createDtos)
    {
        try
        {
            var entities = createDtos.Select(dto =>
            {
                var entity = MapFromCreateDto(dto);
                SetTenantId(entity);
                SetCreateAuditFields(entity);
                return entity;
            }).ToList();

            await Repository.AddRangeAsync(entities);
            await Repository.SaveChangesAsync();

            Logger.LogInformation("Created {Count} {EntityType} entities", entities.Count, typeof(TEntity).Name);

            return entities.Select(MapToDto);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating bulk {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    public virtual async Task UpdateBulkAsync(IEnumerable<Guid> ids, TUpdateDto updateDto)
    {
        try
        {
            var query = Repository.GetQueryable();
            query = ApplyTenantFilter(query);

            var entities = await query.Where(e => ids.Contains(e.Id)).ToListAsync();

            foreach (var entity in entities)
            {
                MapFromUpdateDto(updateDto, entity);
                SetUpdateAuditFields(entity);
            }

            Repository.UpdateRange(entities);
            await Repository.SaveChangesAsync();

            Logger.LogInformation("Updated {Count} {EntityType} entities", entities.Count, typeof(TEntity).Name);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error updating bulk {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    public virtual async Task DeleteBulkAsync(IEnumerable<Guid> ids)
    {
        try
        {
            var query = Repository.GetQueryable();
            query = ApplyTenantFilter(query);

            var entities = await query.Where(e => ids.Contains(e.Id)).ToListAsync();

            foreach (var entity in entities)
            {
                if (entity is ISoftDelete softDeleteEntity)
                {
                    softDeleteEntity.IsDeleted = true;
                    softDeleteEntity.DeletedAt = DateTime.UtcNow;
                    SetUpdateAuditFields(entity);
                }
            }

            if (entities.Any(e => e is ISoftDelete))
            {
                Repository.UpdateRange(entities);
            }
            else
            {
                Repository.DeleteRange(entities);
            }

            await Repository.SaveChangesAsync();

            Logger.LogInformation("Deleted {Count} {EntityType} entities", entities.Count, typeof(TEntity).Name);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting bulk {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }
}
