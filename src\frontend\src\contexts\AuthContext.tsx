import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useAppDispatch, useAppSelector } from '../store/store';
import { 
  getCurrentUser, 
  refreshAccessToken, 
  logout,
  clearError 
} from '../store/slices/authSlice';
import { User } from '../store/slices/authSlice';

interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string, tenantCode?: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  clearAuthError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const { user, token, isAuthenticated, isLoading, error } = useAppSelector((state) => state.auth);

  useEffect(() => {
    // Check if user is authenticated on app start
    const initializeAuth = async () => {
      const storedToken = localStorage.getItem('token');
      
      if (storedToken && !user) {
        try {
          // Try to get current user info
          await dispatch(getCurrentUser()).unwrap();
        } catch (error) {
          // If getting user fails, try to refresh token
          try {
            await dispatch(refreshAccessToken()).unwrap();
            await dispatch(getCurrentUser()).unwrap();
          } catch (refreshError) {
            // If refresh also fails, logout
            console.warn('Failed to refresh token, logging out');
            dispatch(logout());
          }
        }
      }
    };

    initializeAuth();
  }, [dispatch, user]);

  useEffect(() => {
    // Set up token refresh interval
    let refreshInterval: NodeJS.Timeout;

    if (isAuthenticated && token) {
      // Refresh token every 50 minutes (assuming 60-minute expiry)
      refreshInterval = setInterval(async () => {
        try {
          await dispatch(refreshAccessToken()).unwrap();
        } catch (error) {
          console.warn('Failed to refresh token:', error);
          dispatch(logout());
        }
      }, 50 * 60 * 1000); // 50 minutes
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [dispatch, isAuthenticated, token]);

  const login = async (email: string, password: string, tenantCode?: string) => {
    try {
      const { login: loginAction } = await import('../store/slices/authSlice');
      await dispatch(loginAction({ email, password, tenantCode })).unwrap();
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const handleLogout = async () => {
    try {
      await dispatch(logout()).unwrap();
    } catch (error) {
      console.error('Logout failed:', error);
      // Even if logout fails on server, clear local state
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
    }
  };

  const handleRefreshToken = async () => {
    try {
      await dispatch(refreshAccessToken()).unwrap();
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw error;
    }
  };

  const clearAuthError = () => {
    dispatch(clearError());
  };

  const value: AuthContextType = {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout: handleLogout,
    refreshToken: handleRefreshToken,
    clearAuthError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
