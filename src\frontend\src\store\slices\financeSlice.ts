import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { financeApi } from '../../services/api/financeApi';

export interface FeeStructure {
  id: string;
  name: string;
  description?: string;
  academicYearId: string;
  academicYearName: string;
  gradeId?: string;
  gradeName?: string;
  feeType: string;
  amount: number;
  currency: string;
  paymentFrequency: string;
  dueDate?: string;
  isOptional: boolean;
  isActive: boolean;
  discountPercentage?: number;
  lateFeeAmount?: number;
  lateFeeGraceDays?: number;
}

export interface StudentFee {
  id: string;
  studentId: string;
  studentName: string;
  feeStructureId: string;
  feeStructureName: string;
  amount: number;
  currency: string;
  dueDate: string;
  paidAmount: number;
  remainingAmount: number;
  isFullyPaid: boolean;
  status: 'Pending' | 'PartiallyPaid' | 'FullyPaid' | 'Overdue' | 'Waived' | 'Cancelled';
  discountAmount?: number;
  lateFeeAmount?: number;
  notes?: string;
}

export interface Payment {
  id: string;
  studentId: string;
  studentName: string;
  studentFeeId?: string;
  paymentNumber: string;
  amount: number;
  currency: string;
  paymentDate: string;
  paymentMethod: string;
  transactionReference?: string;
  bankReference?: string;
  status: 'Pending' | 'Completed' | 'Failed' | 'Cancelled' | 'Refunded' | 'PartiallyRefunded';
  notes?: string;
  receivedBy?: string;
  receiptPath?: string;
}

export interface Invoice {
  id: string;
  invoiceNumber: string;
  studentId: string;
  studentName: string;
  invoiceDate: string;
  dueDate?: string;
  subTotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  currency: string;
  status: 'Draft' | 'Sent' | 'Paid' | 'Overdue' | 'Cancelled' | 'Refunded';
  notes?: string;
  taxRegistrationNumber?: string;
  eInvoiceReference?: string;
  qrCode?: string;
  filePath?: string;
  items: InvoiceItem[];
}

export interface InvoiceItem {
  id: string;
  invoiceId: string;
  studentFeeId?: string;
  description: string;
  quantity: number;
  unitPrice: number;
  discountAmount: number;
  taxAmount: number;
  totalAmount: number;
}

export interface FinancialReport {
  id: string;
  name: string;
  type: string;
  periodStart: string;
  periodEnd: string;
  generatedAt: string;
  generatedBy?: string;
  reportData?: any;
  filePath?: string;
  status: 'Generated' | 'Approved' | 'Published' | 'Archived';
  notes?: string;
}

export interface FinanceFilters {
  search?: string;
  status?: string;
  paymentMethod?: string;
  dateRange?: { start: string; end: string };
  amountRange?: { min: number; max: number };
  studentId?: string;
  feeType?: string;
}

export interface FinanceState {
  feeStructures: FeeStructure[];
  studentFees: StudentFee[];
  payments: Payment[];
  invoices: Invoice[];
  reports: FinancialReport[];
  currentInvoice: Invoice | null;
  totalRevenue: number;
  pendingAmount: number;
  overdueAmount: number;
  currentPage: number;
  pageSize: number;
  totalCount: number;
  filters: FinanceFilters;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: string | null;
  lastUpdated: number | null;
}

const initialState: FinanceState = {
  feeStructures: [],
  studentFees: [],
  payments: [],
  invoices: [],
  reports: [],
  currentInvoice: null,
  totalRevenue: 0,
  pendingAmount: 0,
  overdueAmount: 0,
  currentPage: 1,
  pageSize: 20,
  totalCount: 0,
  filters: {},
  sortBy: 'paymentDate',
  sortOrder: 'desc',
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
  lastUpdated: null,
};

// Async thunks
export const fetchFeeStructures = createAsyncThunk(
  'finance/fetchFeeStructures',
  async (academicYearId?: string, { rejectWithValue }) => {
    try {
      const response = await financeApi.getFeeStructures(academicYearId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch fee structures');
    }
  }
);

export const fetchStudentFees = createAsyncThunk(
  'finance/fetchStudentFees',
  async (studentId: string, { rejectWithValue }) => {
    try {
      const response = await financeApi.getStudentFees(studentId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch student fees');
    }
  }
);

export const fetchPayments = createAsyncThunk(
  'finance/fetchPayments',
  async (params: {
    page?: number;
    pageSize?: number;
    filters?: FinanceFilters;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}, { rejectWithValue }) => {
    try {
      const response = await financeApi.getPayments(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch payments');
    }
  }
);

export const fetchInvoices = createAsyncThunk(
  'finance/fetchInvoices',
  async (studentId?: string, { rejectWithValue }) => {
    try {
      const response = await financeApi.getInvoices(studentId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch invoices');
    }
  }
);

export const processPayment = createAsyncThunk(
  'finance/processPayment',
  async (paymentData: any, { rejectWithValue }) => {
    try {
      const response = await financeApi.processPayment(paymentData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to process payment');
    }
  }
);

export const generateInvoice = createAsyncThunk(
  'finance/generateInvoice',
  async (data: { studentId: string; feeIds: string[] }, { rejectWithValue }) => {
    try {
      const response = await financeApi.generateInvoice(data.studentId, data.feeIds);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to generate invoice');
    }
  }
);

export const generateFinancialReport = createAsyncThunk(
  'finance/generateReport',
  async (data: {
    reportType: string;
    periodStart: string;
    periodEnd: string;
  }, { rejectWithValue }) => {
    try {
      const response = await financeApi.generateFinancialReport(
        data.reportType,
        data.periodStart,
        data.periodEnd
      );
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to generate report');
    }
  }
);

const financeSlice = createSlice({
  name: 'finance',
  initialState,
  reducers: {
    setCurrentInvoice: (state, action: PayloadAction<Invoice | null>) => {
      state.currentInvoice = action.payload;
    },
    setFilters: (state, action: PayloadAction<FinanceFilters>) => {
      state.filters = action.payload;
      state.currentPage = 1;
    },
    updateFilter: (state, action: PayloadAction<{ key: keyof FinanceFilters; value: any }>) => {
      state.filters[action.payload.key] = action.payload.value;
      state.currentPage = 1;
    },
    clearFilters: (state) => {
      state.filters = {};
      state.currentPage = 1;
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.currentPage = 1;
    },
    setSorting: (state, action: PayloadAction<{ sortBy: string; sortOrder: 'asc' | 'desc' }>) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetFinanceState: (state) => {
      state.currentInvoice = null;
      state.filters = {};
      state.currentPage = 1;
      state.error = null;
    },
    updateFinancialSummary: (state, action: PayloadAction<{
      totalRevenue: number;
      pendingAmount: number;
      overdueAmount: number;
    }>) => {
      state.totalRevenue = action.payload.totalRevenue;
      state.pendingAmount = action.payload.pendingAmount;
      state.overdueAmount = action.payload.overdueAmount;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Fee Structures
      .addCase(fetchFeeStructures.fulfilled, (state, action) => {
        state.feeStructures = action.payload;
      })
      
      // Fetch Student Fees
      .addCase(fetchStudentFees.fulfilled, (state, action) => {
        state.studentFees = action.payload;
      })
      
      // Fetch Payments
      .addCase(fetchPayments.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPayments.fulfilled, (state, action) => {
        state.isLoading = false;
        state.payments = action.payload.items;
        state.totalCount = action.payload.totalCount;
        state.currentPage = action.payload.page;
        state.pageSize = action.payload.pageSize;
        state.lastUpdated = Date.now();
      })
      .addCase(fetchPayments.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch Invoices
      .addCase(fetchInvoices.fulfilled, (state, action) => {
        state.invoices = action.payload;
      })
      
      // Process Payment
      .addCase(processPayment.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(processPayment.fulfilled, (state, action) => {
        state.isCreating = false;
        state.payments.unshift(action.payload);
        state.totalCount += 1;
      })
      .addCase(processPayment.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.payload as string;
      })
      
      // Generate Invoice
      .addCase(generateInvoice.fulfilled, (state, action) => {
        state.invoices.unshift(action.payload);
        state.currentInvoice = action.payload;
      })
      
      // Generate Report
      .addCase(generateFinancialReport.fulfilled, (state, action) => {
        state.reports.unshift(action.payload);
      });
  },
});

export const {
  setCurrentInvoice,
  setFilters,
  updateFilter,
  clearFilters,
  setPage,
  setPageSize,
  setSorting,
  clearError,
  resetFinanceState,
  updateFinancialSummary,
} = financeSlice.actions;

export default financeSlice.reducer;
