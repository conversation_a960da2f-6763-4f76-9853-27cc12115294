using Microsoft.AspNetCore.Http;
using SchoolManagement.Application.DTOs.Common;

namespace SchoolManagement.Application.Interfaces;

public interface IFileStorageService
{
    Task<UploadResultDto> UploadFileAsync(IFormFile file, string folder, string? fileName = null);
    Task<byte[]> DownloadFileAsync(string filePath);
    Task<string> GetFileUrlAsync(string filePath);
    Task DeleteFileAsync(string filePath);
    Task<bool> FileExistsAsync(string filePath);
    Task<long> GetFileSizeAsync(string filePath);
    Task<IEnumerable<string>> GetAllowedExtensionsAsync();
    Task<long> GetMaxFileSizeAsync();
    bool IsValidFileType(string fileName);
    bool IsValidFileSize(long fileSize);
}
