using AutoMapper;
using Microsoft.Extensions.Logging;
using SchoolManagement.Application.DTOs;
using SchoolManagement.Core.Entities;
using SchoolManagement.Core.Interfaces;
using Newtonsoft.Json;

namespace SchoolManagement.Application.Services;

/// <summary>
/// Service for tenant management operations
/// </summary>
public class TenantService : ITenantService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<TenantService> _logger;

    public TenantService(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<TenantService> logger)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<Tenant?> GetTenantByIdAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Repository<Tenant>().GetByIdAsync(tenantId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant by ID: {TenantId}", tenantId);
            throw;
        }
    }

    public async Task<Tenant?> GetTenantByCodeAsync(string code, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Repository<Tenant>()
                .FindSingleAsync(t => t.Code == code, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant by code: {Code}", code);
            throw;
        }
    }

    public async Task<IEnumerable<Tenant>> GetAllTenantsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _unitOfWork.Repository<Tenant>().GetAllAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all tenants");
            throw;
        }
    }

    public async Task<Tenant> CreateTenantAsync(Tenant tenant, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate tenant code uniqueness
            var existingTenant = await GetTenantByCodeAsync(tenant.Code, cancellationToken);
            if (existingTenant != null)
            {
                throw new InvalidOperationException($"Tenant with code '{tenant.Code}' already exists");
            }

            // Validate email uniqueness
            var existingEmailTenant = await _unitOfWork.Repository<Tenant>()
                .FindSingleAsync(t => t.ContactEmail == tenant.ContactEmail, cancellationToken);
            if (existingEmailTenant != null)
            {
                throw new InvalidOperationException($"Tenant with email '{tenant.ContactEmail}' already exists");
            }

            // Set default values
            tenant.Id = Guid.NewGuid();
            tenant.IsActive = true;
            tenant.CreatedAt = DateTime.UtcNow;

            // Generate encryption key for tenant
            tenant.EncryptionKey = GenerateEncryptionKey();

            await _unitOfWork.Repository<Tenant>().AddAsync(tenant, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created new tenant: {TenantId} - {TenantName}", tenant.Id, tenant.Name);

            return tenant;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating tenant: {TenantName}", tenant.Name);
            throw;
        }
    }

    public async Task<Tenant> UpdateTenantAsync(Tenant tenant, CancellationToken cancellationToken = default)
    {
        try
        {
            var existingTenant = await GetTenantByIdAsync(tenant.Id, cancellationToken);
            if (existingTenant == null)
            {
                throw new InvalidOperationException($"Tenant with ID '{tenant.Id}' not found");
            }

            // Validate email uniqueness (excluding current tenant)
            var existingEmailTenant = await _unitOfWork.Repository<Tenant>()
                .FindSingleAsync(t => t.ContactEmail == tenant.ContactEmail && t.Id != tenant.Id, cancellationToken);
            if (existingEmailTenant != null)
            {
                throw new InvalidOperationException($"Tenant with email '{tenant.ContactEmail}' already exists");
            }

            // Update properties
            existingTenant.Name = tenant.Name;
            existingTenant.Description = tenant.Description;
            existingTenant.ContactEmail = tenant.ContactEmail;
            existingTenant.ContactPhone = tenant.ContactPhone;
            existingTenant.Address = tenant.Address;
            existingTenant.City = tenant.City;
            existingTenant.State = tenant.State;
            existingTenant.PostalCode = tenant.PostalCode;
            existingTenant.Country = tenant.Country;
            existingTenant.IsActive = tenant.IsActive;
            existingTenant.SubscriptionStartDate = tenant.SubscriptionStartDate;
            existingTenant.SubscriptionEndDate = tenant.SubscriptionEndDate;
            existingTenant.SubscriptionPlan = tenant.SubscriptionPlan;
            existingTenant.MaxStudents = tenant.MaxStudents;
            existingTenant.MaxStaff = tenant.MaxStaff;
            existingTenant.Settings = tenant.Settings;
            existingTenant.BrandingConfig = tenant.BrandingConfig;
            existingTenant.ComplianceConfig = tenant.ComplianceConfig;
            existingTenant.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Repository<Tenant>().UpdateAsync(existingTenant, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated tenant: {TenantId} - {TenantName}", tenant.Id, tenant.Name);

            return existingTenant;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant: {TenantId}", tenant.Id);
            throw;
        }
    }

    public async Task DeleteTenantAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await GetTenantByIdAsync(tenantId, cancellationToken);
            if (tenant == null)
            {
                throw new InvalidOperationException($"Tenant with ID '{tenantId}' not found");
            }

            // Check if tenant has active users, students, or staff
            var hasUsers = await _unitOfWork.TenantRepository<User>()
                .CountByTenantAsync(tenantId, cancellationToken: cancellationToken) > 0;
            
            if (hasUsers)
            {
                throw new InvalidOperationException("Cannot delete tenant with active users. Please deactivate the tenant instead.");
            }

            await _unitOfWork.Repository<Tenant>().DeleteAsync(tenant, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Deleted tenant: {TenantId} - {TenantName}", tenantId, tenant.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting tenant: {TenantId}", tenantId);
            throw;
        }
    }

    public async Task<bool> IsTenantActiveAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await GetTenantByIdAsync(tenantId, cancellationToken);
            if (tenant == null)
            {
                return false;
            }

            // Check if tenant is active and subscription is valid
            var isActive = tenant.IsActive;
            var hasValidSubscription = !tenant.SubscriptionEndDate.HasValue || 
                                     tenant.SubscriptionEndDate.Value > DateTime.UtcNow;

            return isActive && hasValidSubscription;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking tenant active status: {TenantId}", tenantId);
            return false;
        }
    }

    public async Task<TenantInfo?> GetTenantInfoAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await GetTenantByIdAsync(tenantId, cancellationToken);
            if (tenant == null)
            {
                return null;
            }

            return new TenantInfo
            {
                Id = tenant.Id,
                Name = tenant.Name,
                Code = tenant.Code,
                IsActive = tenant.IsActive,
                DatabaseConnectionString = tenant.DatabaseConnectionString,
                Settings = ParseJsonToDictionary(tenant.Settings),
                BrandingConfig = ParseJsonToDictionary(tenant.BrandingConfig),
                ComplianceConfig = ParseJsonToDictionary(tenant.ComplianceConfig)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant info: {TenantId}", tenantId);
            throw;
        }
    }

    private static string GenerateEncryptionKey()
    {
        // Generate a secure encryption key for tenant-specific data encryption
        var key = new byte[32]; // 256-bit key
        using var rng = System.Security.Cryptography.RandomNumberGenerator.Create();
        rng.GetBytes(key);
        return Convert.ToBase64String(key);
    }

    private static Dictionary<string, object>? ParseJsonToDictionary(string? json)
    {
        if (string.IsNullOrEmpty(json))
        {
            return null;
        }

        try
        {
            return JsonConvert.DeserializeObject<Dictionary<string, object>>(json);
        }
        catch
        {
            return null;
        }
    }
}
