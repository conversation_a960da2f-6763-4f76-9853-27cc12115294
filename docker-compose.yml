version: '3.8'

services:
  # SQL Server Database
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: school-management-db
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=SchoolManagement123!
      - MSSQL_PID=Developer
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    networks:
      - school-management-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: school-management-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - school-management-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # School Management API
  api:
    build:
      context: .
      dockerfile: src/backend/SchoolManagement.Api/Dockerfile
    container_name: school-management-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:5000
      - ConnectionStrings__DefaultConnection=Server=sqlserver,1433;Database=SchoolManagementDb;User Id=sa;Password=SchoolManagement123!;TrustServerCertificate=true;MultipleActiveResultSets=true
      - Redis__ConnectionString=redis:6379
      - Jwt__Key=YourSuperSecretKeyThatIsAtLeast32CharactersLong!
      - Jwt__Issuer=SchoolManagementAPI
      - Jwt__Audience=SchoolManagementClients
    ports:
      - "5000:5000"
    depends_on:
      - sqlserver
      - redis
    networks:
      - school-management-network
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads

  # React Frontend
  frontend:
    build:
      context: .
      dockerfile: src/frontend/Dockerfile
    container_name: school-management-frontend
    environment:
      - REACT_APP_API_URL=http://localhost:5000/api
      - REACT_APP_ENVIRONMENT=development
    ports:
      - "3000:3000"
    depends_on:
      - api
    networks:
      - school-management-network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: school-management-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api
      - frontend
    networks:
      - school-management-network
    restart: unless-stopped

volumes:
  sqlserver_data:
    driver: local
  redis_data:
    driver: local

networks:
  school-management-network:
    driver: bridge
