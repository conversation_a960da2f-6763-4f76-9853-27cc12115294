import { apiHelpers, PaginatedResponse } from './apiClient';
import { Staff } from '../../store/slices/staffSlice';

export interface CreateStaffRequest {
  employeeNumber: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  arabicFirstName?: string;
  arabicLastName?: string;
  arabicMiddleName?: string;
  dateOfBirth: string;
  gender: string;
  nationality?: string;
  nationalId?: string;
  passportNumber?: string;
  placeOfBirth?: string;
  emergencyContactPhone?: string;
  emergencyContactName?: string;
  emergencyContactRelation?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  hireDate?: string;
  position: string;
  department?: string;
  specialization?: string;
  salary: number;
  salaryCurrency?: string;
  payrollFrequency?: string;
  bankAccount?: string;
  bankName?: string;
  bankBranch?: string;
  experienceYears?: number;
  qualifications?: any;
  certifications?: any;
  medicalInfo?: any;
  notes?: string;
  email: string;
  phoneNumber?: string;
}

export interface UpdateStaffRequest {
  id: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  arabicFirstName?: string;
  arabicLastName?: string;
  arabicMiddleName?: string;
  dateOfBirth: string;
  gender: string;
  nationality?: string;
  nationalId?: string;
  passportNumber?: string;
  placeOfBirth?: string;
  emergencyContactPhone?: string;
  emergencyContactName?: string;
  emergencyContactRelation?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  status: string;
  position: string;
  department?: string;
  specialization?: string;
  salary: number;
  salaryCurrency?: string;
  payrollFrequency?: string;
  bankAccount?: string;
  bankName?: string;
  bankBranch?: string;
  experienceYears?: number;
  qualifications?: any;
  certifications?: any;
  medicalInfo?: any;
  notes?: string;
  email: string;
  phoneNumber?: string;
}

export interface StaffFilters {
  search?: string;
  department?: string;
  position?: string;
  status?: string;
  gender?: string;
  nationality?: string;
  hireYear?: string;
  experienceYears?: { min?: number; max?: number };
  salary?: { min?: number; max?: number };
}

export const staffApi = {
  // Get staff with pagination and filters
  getStaff: async (params?: {
    page?: number;
    pageSize?: number;
    filters?: StaffFilters;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<PaginatedResponse<Staff>> => {
    return apiHelpers.get('/staff', { params });
  },

  // Get staff member by ID
  getStaffById: async (id: string): Promise<Staff> => {
    return apiHelpers.get(`/staff/${id}`);
  },

  // Get staff member by employee number
  getStaffByEmployeeNumber: async (employeeNumber: string): Promise<Staff> => {
    return apiHelpers.get(`/staff/by-employee-number/${employeeNumber}`);
  },

  // Create new staff member
  createStaff: async (staffData: CreateStaffRequest): Promise<Staff> => {
    return apiHelpers.post('/staff', staffData);
  },

  // Update staff member
  updateStaff: async (id: string, staffData: UpdateStaffRequest): Promise<Staff> => {
    return apiHelpers.put(`/staff/${id}`, staffData);
  },

  // Delete staff member
  deleteStaff: async (id: string): Promise<void> => {
    return apiHelpers.delete(`/staff/${id}`);
  },

  // Get staff by department
  getStaffByDepartment: async (department: string): Promise<Staff[]> => {
    return apiHelpers.get(`/staff/by-department/${department}`);
  },

  // Get teachers only
  getTeachers: async (): Promise<Staff[]> => {
    return apiHelpers.get('/staff/teachers');
  },

  // Upload staff profile picture
  uploadStaffProfilePicture: async (staffId: string, file: File): Promise<{ profilePictureUrl: string }> => {
    return apiHelpers.upload(`/staff/${staffId}/profile-picture`, file);
  },

  // Get staff documents
  getStaffDocuments: async (staffId: string): Promise<Array<{
    id: string;
    documentType: string;
    fileName: string;
    filePath: string;
    contentType: string;
    fileSize: number;
    description?: string;
    expiryDate?: string;
    isRequired: boolean;
    isVerified: boolean;
    verifiedAt?: string;
    verifiedBy?: string;
    uploadedAt: string;
  }>> => {
    return apiHelpers.get(`/staff/${staffId}/documents`);
  },

  // Upload staff document
  uploadStaffDocument: async (staffId: string, file: File, documentType: string, description?: string): Promise<{
    id: string;
    fileName: string;
    filePath: string;
    documentType: string;
  }> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('documentType', documentType);
    if (description) {
      formData.append('description', description);
    }
    return apiHelpers.post(`/staff/${staffId}/documents`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },

  // Delete staff document
  deleteStaffDocument: async (staffId: string, documentId: string): Promise<void> => {
    return apiHelpers.delete(`/staff/${staffId}/documents/${documentId}`);
  },

  // Get staff leave requests
  getStaffLeaves: async (staffId: string): Promise<Array<{
    id: string;
    leaveType: string;
    startDate: string;
    endDate: string;
    totalDays: number;
    reason: string;
    comments?: string;
    status: string;
    approvedAt?: string;
    approvedBy?: string;
    approvalComments?: string;
  }>> => {
    return apiHelpers.get(`/staff/${staffId}/leaves`);
  },

  // Request staff leave
  requestStaffLeave: async (staffId: string, leaveData: {
    leaveType: string;
    startDate: string;
    endDate: string;
    reason: string;
    comments?: string;
  }): Promise<{
    id: string;
    message: string;
  }> => {
    return apiHelpers.post(`/staff/${staffId}/leaves`, leaveData);
  },

  // Approve/Reject staff leave
  updateLeaveStatus: async (leaveId: string, status: 'Approved' | 'Rejected', comments?: string): Promise<void> => {
    return apiHelpers.put(`/staff/leaves/${leaveId}/status`, { status, comments });
  },

  // Get staff attendance
  getStaffAttendance: async (staffId: string, startDate: string, endDate: string): Promise<Array<{
    id: string;
    date: string;
    checkInTime?: string;
    checkOutTime?: string;
    breakStartTime?: string;
    breakEndTime?: string;
    totalWorkingHours?: string;
    totalBreakTime?: string;
    status: string;
    notes?: string;
    isLate: boolean;
    isEarlyLeave: boolean;
    lateBy?: string;
    earlyLeaveBy?: string;
  }>> => {
    return apiHelpers.get(`/staff/${staffId}/attendance`, {
      params: { startDate, endDate }
    });
  },

  // Mark staff attendance
  markStaffAttendance: async (staffId: string, attendanceData: {
    date: string;
    checkInTime?: string;
    checkOutTime?: string;
    breakStartTime?: string;
    breakEndTime?: string;
    status: string;
    notes?: string;
  }): Promise<{
    id: string;
    message: string;
  }> => {
    return apiHelpers.post(`/staff/${staffId}/attendance`, attendanceData);
  },

  // Get staff payroll
  getStaffPayroll: async (staffId: string, year?: number): Promise<Array<{
    id: string;
    payPeriodStart: string;
    payPeriodEnd: string;
    basicSalary: number;
    allowances: number;
    overtime: number;
    bonuses: number;
    deductions: number;
    tax: number;
    netSalary: number;
    currency: string;
    status: string;
    processedAt?: string;
    processedBy?: string;
  }>> => {
    return apiHelpers.get(`/staff/${staffId}/payroll`, { params: { year } });
  },

  // Process staff payroll
  processStaffPayroll: async (staffId: string, payrollData: {
    payPeriodStart: string;
    payPeriodEnd: string;
    basicSalary: number;
    allowances?: number;
    overtime?: number;
    bonuses?: number;
    deductions?: number;
    tax?: number;
    notes?: string;
  }): Promise<{
    id: string;
    netSalary: number;
    message: string;
  }> => {
    return apiHelpers.post(`/staff/${staffId}/payroll`, payrollData);
  },

  // Get staff performance evaluations
  getStaffEvaluations: async (staffId: string): Promise<Array<{
    id: string;
    evaluationPeriod: string;
    evaluatedBy: string;
    overallRating: number;
    strengths: string[];
    areasForImprovement: string[];
    goals: string[];
    comments: string;
    evaluationDate: string;
  }>> => {
    return apiHelpers.get(`/staff/${staffId}/evaluations`);
  },

  // Create staff performance evaluation
  createStaffEvaluation: async (staffId: string, evaluationData: {
    evaluationPeriod: string;
    overallRating: number;
    strengths: string[];
    areasForImprovement: string[];
    goals: string[];
    comments: string;
  }): Promise<{
    id: string;
    message: string;
  }> => {
    return apiHelpers.post(`/staff/${staffId}/evaluations`, evaluationData);
  },

  // Get staff schedule
  getStaffSchedule: async (staffId: string, startDate?: string, endDate?: string): Promise<Array<{
    id: string;
    dayOfWeek: string;
    startTime: string;
    endTime: string;
    subjectName: string;
    gradeName: string;
    classroom: string;
    isActive: boolean;
  }>> => {
    return apiHelpers.get(`/staff/${staffId}/schedule`, {
      params: { startDate, endDate }
    });
  },

  // Assign staff to class
  assignStaffToClass: async (staffId: string, classScheduleId: string): Promise<void> => {
    return apiHelpers.post(`/staff/${staffId}/assign-class`, { classScheduleId });
  },

  // Remove staff from class
  removeStaffFromClass: async (staffId: string, classScheduleId: string): Promise<void> => {
    return apiHelpers.delete(`/staff/${staffId}/classes/${classScheduleId}`);
  },

  // Export staff data
  exportStaff: async (filters?: StaffFilters, format: 'csv' | 'excel' = 'excel'): Promise<void> => {
    return apiHelpers.download('/staff/export', `staff.${format}`, {
      params: { ...filters, format }
    });
  },

  // Import staff data
  importStaff: async (file: File): Promise<{
    message: string;
    importedCount: number;
    errors?: Array<{
      row: number;
      field: string;
      message: string;
    }>;
  }> => {
    return apiHelpers.upload('/staff/import', file);
  },

  // Bulk update staff
  bulkUpdateStaff: async (staffIds: string[], updates: Partial<UpdateStaffRequest>): Promise<{
    updatedCount: number;
    errors?: Array<{
      staffId: string;
      message: string;
    }>;
  }> => {
    return apiHelpers.post('/staff/bulk-update', { staffIds, updates });
  },

  // Get staff statistics
  getStaffStatistics: async (): Promise<{
    totalStaff: number;
    activeStaff: number;
    onLeaveStaff: number;
    departmentBreakdown: Array<{
      department: string;
      count: number;
    }>;
    positionBreakdown: Array<{
      position: string;
      count: number;
    }>;
    averageSalary: number;
    averageExperience: number;
  }> => {
    return apiHelpers.get('/staff/statistics');
  },
};
