import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { staffApi } from '../../services/api/staffApi';

export interface Staff {
  id: string;
  employeeNumber: string;
  userId: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  fullName: string;
  displayName: string;
  arabicFirstName?: string;
  arabicLastName?: string;
  arabicMiddleName?: string;
  arabicFullName?: string;
  dateOfBirth: string;
  age: number;
  gender: string;
  nationality?: string;
  nationalId?: string;
  passportNumber?: string;
  placeOfBirth?: string;
  emergencyContactPhone?: string;
  emergencyContactName?: string;
  emergencyContactRelation?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  hireDate: string;
  terminationDate?: string;
  status: 'Active' | 'Inactive' | 'OnLeave' | 'Terminated' | 'Suspended' | 'Retired';
  position: string;
  department?: string;
  specialization?: string;
  salary: number;
  salaryCurrency: string;
  payrollFrequency: string;
  bankAccount?: string;
  bankName?: string;
  bankBranch?: string;
  experienceYears: number;
  qualifications?: any;
  certifications?: any;
  profilePictureUrl?: string;
  medicalInfo?: any;
  notes?: string;
  email: string;
  phoneNumber?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface StaffFilters {
  search?: string;
  department?: string;
  position?: string;
  status?: string;
  gender?: string;
  nationality?: string;
  hireYear?: string;
  experienceYears?: { min?: number; max?: number };
  salary?: { min?: number; max?: number };
}

export interface StaffState {
  staff: Staff[];
  currentStaff: Staff | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
  filters: StaffFilters;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: string | null;
  lastUpdated: number | null;
}

const initialState: StaffState = {
  staff: [],
  currentStaff: null,
  totalCount: 0,
  currentPage: 1,
  pageSize: 20,
  filters: {},
  sortBy: 'lastName',
  sortOrder: 'asc',
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
  lastUpdated: null,
};

// Async thunks
export const fetchStaff = createAsyncThunk(
  'staff/fetchStaff',
  async (params: {
    page?: number;
    pageSize?: number;
    filters?: StaffFilters;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}, { rejectWithValue }) => {
    try {
      const response = await staffApi.getStaff(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch staff');
    }
  }
);

export const fetchStaffById = createAsyncThunk(
  'staff/fetchById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await staffApi.getStaffById(id);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch staff member');
    }
  }
);

export const createStaff = createAsyncThunk(
  'staff/create',
  async (staffData: any, { rejectWithValue }) => {
    try {
      const response = await staffApi.createStaff(staffData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create staff member');
    }
  }
);

export const updateStaff = createAsyncThunk(
  'staff/update',
  async ({ id, data }: { id: string; data: any }, { rejectWithValue }) => {
    try {
      const response = await staffApi.updateStaff(id, data);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update staff member');
    }
  }
);

export const deleteStaff = createAsyncThunk(
  'staff/delete',
  async (id: string, { rejectWithValue }) => {
    try {
      await staffApi.deleteStaff(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete staff member');
    }
  }
);

const staffSlice = createSlice({
  name: 'staff',
  initialState,
  reducers: {
    setCurrentStaff: (state, action: PayloadAction<Staff | null>) => {
      state.currentStaff = action.payload;
    },
    setFilters: (state, action: PayloadAction<StaffFilters>) => {
      state.filters = action.payload;
      state.currentPage = 1;
    },
    updateFilter: (state, action: PayloadAction<{ key: keyof StaffFilters; value: any }>) => {
      state.filters[action.payload.key] = action.payload.value;
      state.currentPage = 1;
    },
    clearFilters: (state) => {
      state.filters = {};
      state.currentPage = 1;
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.currentPage = 1;
    },
    setSorting: (state, action: PayloadAction<{ sortBy: string; sortOrder: 'asc' | 'desc' }>) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetStaffState: (state) => {
      state.currentStaff = null;
      state.filters = {};
      state.currentPage = 1;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Staff
      .addCase(fetchStaff.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchStaff.fulfilled, (state, action) => {
        state.isLoading = false;
        state.staff = action.payload.items;
        state.totalCount = action.payload.totalCount;
        state.currentPage = action.payload.page;
        state.pageSize = action.payload.pageSize;
        state.lastUpdated = Date.now();
      })
      .addCase(fetchStaff.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch Staff by ID
      .addCase(fetchStaffById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchStaffById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentStaff = action.payload;
      })
      .addCase(fetchStaffById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Create Staff
      .addCase(createStaff.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createStaff.fulfilled, (state, action) => {
        state.isCreating = false;
        state.staff.unshift(action.payload);
        state.totalCount += 1;
        state.currentStaff = action.payload;
      })
      .addCase(createStaff.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.payload as string;
      })
      
      // Update Staff
      .addCase(updateStaff.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateStaff.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.staff.findIndex(s => s.id === action.payload.id);
        if (index !== -1) {
          state.staff[index] = action.payload;
        }
        if (state.currentStaff?.id === action.payload.id) {
          state.currentStaff = action.payload;
        }
      })
      .addCase(updateStaff.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      })
      
      // Delete Staff
      .addCase(deleteStaff.pending, (state) => {
        state.isDeleting = true;
        state.error = null;
      })
      .addCase(deleteStaff.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.staff = state.staff.filter(s => s.id !== action.payload);
        state.totalCount = Math.max(0, state.totalCount - 1);
        if (state.currentStaff?.id === action.payload) {
          state.currentStaff = null;
        }
      })
      .addCase(deleteStaff.rejected, (state, action) => {
        state.isDeleting = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setCurrentStaff,
  setFilters,
  updateFilter,
  clearFilters,
  setPage,
  setPageSize,
  setSorting,
  clearError,
  resetStaffState,
} = staffSlice.actions;

export default staffSlice.reducer;
