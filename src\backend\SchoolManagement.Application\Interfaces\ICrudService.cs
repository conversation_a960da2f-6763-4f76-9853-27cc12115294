using SchoolManagement.Application.DTOs.Common;
using SchoolManagement.Core.Entities;
using System.Linq.Expressions;

namespace SchoolManagement.Application.Interfaces;

public interface ICrudService<TEntity, TDto, TCreateDto, TUpdateDto>
    where TEntity : BaseEntity
    where TDto : class
    where TCreateDto : class
    where TUpdateDto : class
{
    Task<PagedResultDto<TDto>> GetAllAsync(
        int page = 1,
        int pageSize = 20,
        string? search = null,
        string? sortBy = null,
        string? sortOrder = "asc",
        Expression<Func<TEntity, bool>>? filter = null);

    Task<TDto?> GetByIdAsync(Guid id);
    Task<TDto> CreateAsync(TCreateDto createDto);
    Task<TDto> UpdateAsync(Guid id, TUpdateDto updateDto);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task<int> CountAsync(Expression<Func<TEntity, bool>>? filter = null);

    // Bulk operations
    Task<IEnumerable<TDto>> CreateBulkAsync(IEnumerable<TCreateDto> createDtos);
    Task UpdateBulkAsync(IEnumerable<Guid> ids, TUpdateDto updateDto);
    Task DeleteBulkAsync(IEnumerable<Guid> ids);
}
