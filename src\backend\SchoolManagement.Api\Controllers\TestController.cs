using Microsoft.AspNetCore.Mvc;

namespace SchoolManagement.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TestController : ControllerBase
{
    private readonly ILogger<TestController> _logger;

    public TestController(ILogger<TestController> logger)
    {
        _logger = logger;
    }

    [HttpGet]
    public ActionResult<object> Get()
    {
        return Ok(new
        {
            Message = "School Management System API is running!",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0",
            Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development"
        });
    }

    [HttpGet("health")]
    public ActionResult<object> Health()
    {
        return Ok(new
        {
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Uptime = Environment.TickCount64
        });
    }

    [HttpPost("echo")]
    public ActionResult<object> Echo([FromBody] object data)
    {
        return Ok(new
        {
            Echo = data,
            Timestamp = DateTime.UtcNow
        });
    }
}
