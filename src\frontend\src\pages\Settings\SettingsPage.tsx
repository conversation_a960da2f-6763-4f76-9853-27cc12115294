import React, { useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box, Typography } from '@mui/material';
import { useAppDispatch } from '../../store/store';
import { setPageTitle, setBreadcrumbs } from '../../store/slices/uiSlice';

const SettingsListPage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setPageTitle('Settings'));
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', path: '/dashboard' },
      { label: 'Settings' }
    ]));
  }, [dispatch]);

  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 700, mb: 3 }}>
        System Settings
      </Typography>
      <Typography variant="body1" color="text.secondary">
        System settings functionality will be implemented here.
      </Typography>
    </Box>
  );
};

const SettingsPage: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<SettingsListPage />} />
      <Route path="/general" element={<SettingsListPage />} />
      <Route path="/users" element={<SettingsListPage />} />
      <Route path="/system" element={<SettingsListPage />} />
    </Routes>
  );
};

export default SettingsPage;
