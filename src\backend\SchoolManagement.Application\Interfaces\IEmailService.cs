namespace SchoolManagement.Application.Interfaces;

public interface IEmailService
{
    Task SendEmailAsync(string to, string subject, string body, bool isHtml = true);
    Task SendEmailVerificationAsync(string email, string firstName, string token);
    Task SendPasswordResetAsync(string email, string firstName, string token);
    Task SendWelcomeEmailAsync(string email, string firstName, string temporaryPassword);
    Task SendNotificationEmailAsync(string email, string subject, string message);
    Task SendBulkEmailAsync(IEnumerable<string> recipients, string subject, string body, bool isHtml = true);
}
