import React, { useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box, Typography } from '@mui/material';
import { useAppDispatch } from '../../store/store';
import { setPageTitle, setBreadcrumbs } from '../../store/slices/uiSlice';

const FinanceListPage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setPageTitle('Finance'));
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', path: '/dashboard' },
      { label: 'Finance' }
    ]));
  }, [dispatch]);

  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 700, mb: 3 }}>
        Financial Management
      </Typography>
      <Typography variant="body1" color="text.secondary">
        Financial management functionality will be implemented here.
      </Typography>
    </Box>
  );
};

const FinancePage: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<FinanceListPage />} />
      <Route path="/fees" element={<FinanceListPage />} />
      <Route path="/payments" element={<FinanceListPage />} />
      <Route path="/reports" element={<FinanceListPage />} />
    </Routes>
  );
};

export default FinancePage;
