namespace SchoolManagement.Application.DTOs.Common;

public class PagedResultDto<T>
{
    public IEnumerable<T> Items { get; set; } = new List<T>();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasNextPage { get; set; }
    public bool HasPreviousPage { get; set; }

    public PagedResultDto()
    {
    }

    public PagedResultDto(IEnumerable<T> items, int totalCount, int page, int pageSize)
    {
        Items = items;
        TotalCount = totalCount;
        Page = page;
        PageSize = pageSize;
        TotalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        HasNextPage = page < TotalPages;
        HasPreviousPage = page > 1;
    }
}

public class UploadResultDto
{
    public string FileName { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public string FileUrl { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string ContentType { get; set; } = string.Empty;
    public DateTime UploadedAt { get; set; }
}

public class ImportResultDto
{
    public string Message { get; set; } = string.Empty;
    public int ImportedCount { get; set; }
    public int TotalCount { get; set; }
    public int ErrorCount { get; set; }
    public IEnumerable<ImportErrorDto> Errors { get; set; } = new List<ImportErrorDto>();
}

public class ImportErrorDto
{
    public int Row { get; set; }
    public string Field { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
}

public class ExportResultDto
{
    public byte[] Data { get; set; } = Array.Empty<byte>();
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
}

public class ApiResponseDto<T>
{
    public T? Data { get; set; }
    public string Message { get; set; } = string.Empty;
    public bool Success { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public IEnumerable<string> Errors { get; set; } = new List<string>();

    public ApiResponseDto()
    {
    }

    public ApiResponseDto(T data, string message = "")
    {
        Data = data;
        Message = message;
        Success = true;
    }

    public ApiResponseDto(string error)
    {
        Message = error;
        Success = false;
        Errors = new List<string> { error };
    }

    public ApiResponseDto(IEnumerable<string> errors)
    {
        Success = false;
        Errors = errors;
        Message = "Validation errors occurred";
    }
}

public class ValidationErrorDto
{
    public string Field { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public object? AttemptedValue { get; set; }
}

public class ErrorResponseDto
{
    public string Message { get; set; } = string.Empty;
    public string? Details { get; set; }
    public string Type { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public IEnumerable<ValidationErrorDto> Errors { get; set; } = new List<ValidationErrorDto>();
}
