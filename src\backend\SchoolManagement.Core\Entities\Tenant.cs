using System.ComponentModel.DataAnnotations;

namespace SchoolManagement.Core.Entities;

/// <summary>
/// Represents a tenant (school branch) in the multi-tenant system
/// </summary>
public class Tenant : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(50)]
    public string Code { get; set; } = string.Empty; // Unique identifier for the tenant
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string ContactEmail { get; set; } = string.Empty;
    
    [MaxLength(20)]
    public string? ContactPhone { get; set; }
    
    [MaxLength(500)]
    public string? Address { get; set; }
    
    [MaxLength(100)]
    public string? City { get; set; }
    
    [MaxLength(100)]
    public string? State { get; set; }
    
    [MaxLength(20)]
    public string? PostalCode { get; set; }
    
    [MaxLength(100)]
    public string? Country { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public DateTime? SubscriptionStartDate { get; set; }
    
    public DateTime? SubscriptionEndDate { get; set; }
    
    [MaxLength(50)]
    public string? SubscriptionPlan { get; set; }
    
    public int MaxStudents { get; set; } = 1000;
    
    public int MaxStaff { get; set; } = 100;
    
    /// <summary>
    /// Tenant-specific configuration settings stored as JSON
    /// </summary>
    public string? Settings { get; set; }
    
    /// <summary>
    /// Branding configuration (logo, colors, themes) stored as JSON
    /// </summary>
    public string? BrandingConfig { get; set; }
    
    /// <summary>
    /// Regional compliance settings (Egypt/Saudi specific configurations)
    /// </summary>
    public string? ComplianceConfig { get; set; }
    
    /// <summary>
    /// Database connection string for tenant-specific database (if using database-per-tenant)
    /// </summary>
    public string? DatabaseConnectionString { get; set; }
    
    /// <summary>
    /// Encryption key for tenant-specific data encryption
    /// </summary>
    public string? EncryptionKey { get; set; }
    
    // Navigation properties
    public virtual ICollection<User> Users { get; set; } = new List<User>();
    public virtual ICollection<Student> Students { get; set; } = new List<Student>();
    public virtual ICollection<Staff> Staff { get; set; } = new List<Staff>();
    public virtual ICollection<AcademicYear> AcademicYears { get; set; } = new List<AcademicYear>();
    public virtual ICollection<Grade> Grades { get; set; } = new List<Grade>();
    public virtual ICollection<Subject> Subjects { get; set; } = new List<Subject>();
}

/// <summary>
/// Tenant subscription plans
/// </summary>
public enum SubscriptionPlan
{
    Basic,
    Standard,
    Premium,
    Enterprise
}

/// <summary>
/// Tenant status enumeration
/// </summary>
public enum TenantStatus
{
    Active,
    Suspended,
    Inactive,
    Trial,
    Expired
}
