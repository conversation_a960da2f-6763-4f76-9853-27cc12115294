import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { attendanceApi } from '../../services/api/attendanceApi';

export interface StudentAttendance {
  id: string;
  studentId: string;
  studentName: string;
  date: string;
  subjectId?: string;
  subjectName?: string;
  classScheduleId?: string;
  status: 'Present' | 'Absent' | 'Late' | 'HalfDay' | 'OnLeave' | 'Holiday';
  checkInTime?: string;
  checkOutTime?: string;
  isLate: boolean;
  lateBy?: string;
  isEarlyLeave: boolean;
  earlyLeaveBy?: string;
  notes?: string;
  reason?: string;
  markedBy?: string;
  markedAt?: string;
  attendanceMethod?: string;
  attendanceLocation?: string;
}

export interface AttendanceSummary {
  id: string;
  studentId: string;
  studentName: string;
  periodStart: string;
  periodEnd: string;
  periodType: string;
  totalDays: number;
  presentDays: number;
  absentDays: number;
  lateDays: number;
  halfDays: number;
  leaveDays: number;
  attendancePercentage: number;
  totalLateTime?: string;
  averageLateTime?: string;
}

export interface StudentLeave {
  id: string;
  studentId: string;
  studentName: string;
  leaveType: string;
  startDate: string;
  endDate: string;
  totalDays: number;
  reason: string;
  comments?: string;
  status: 'Pending' | 'Approved' | 'Rejected' | 'Cancelled';
  approvedAt?: string;
  approvedBy?: string;
  approvalComments?: string;
  requestedBy?: string;
  supportingDocuments?: string;
}

export interface AttendancePolicy {
  id: string;
  name: string;
  description?: string;
  gradeId?: string;
  gradeName?: string;
  minimumAttendancePercentage: number;
  lateGracePeriodMinutes: number;
  maxLateDaysPerTerm: number;
  maxAbsentDaysPerTerm: number;
  autoMarkAbsentAfterMinutes: number;
  notifyParentsAfterAbsentDays: number;
  requireMedicalCertificateAfterDays: number;
  isActive: boolean;
  policyConfig?: any;
}

export interface AttendanceFilters {
  search?: string;
  grade?: string;
  status?: string;
  date?: string;
  dateRange?: { start: string; end: string };
  subject?: string;
}

export interface AttendanceState {
  attendances: StudentAttendance[];
  summaries: AttendanceSummary[];
  leaves: StudentLeave[];
  policies: AttendancePolicy[];
  currentAttendance: StudentAttendance | null;
  currentSummary: AttendanceSummary | null;
  selectedDate: string;
  selectedGrade: string | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
  filters: AttendanceFilters;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  isLoading: boolean;
  isMarking: boolean;
  isUpdating: boolean;
  error: string | null;
  lastUpdated: number | null;
}

const initialState: AttendanceState = {
  attendances: [],
  summaries: [],
  leaves: [],
  policies: [],
  currentAttendance: null,
  currentSummary: null,
  selectedDate: new Date().toISOString().split('T')[0],
  selectedGrade: null,
  totalCount: 0,
  currentPage: 1,
  pageSize: 50,
  filters: {},
  sortBy: 'studentName',
  sortOrder: 'asc',
  isLoading: false,
  isMarking: false,
  isUpdating: false,
  error: null,
  lastUpdated: null,
};

// Async thunks
export const fetchStudentAttendance = createAsyncThunk(
  'attendance/fetchStudentAttendance',
  async (params: {
    studentId: string;
    startDate: string;
    endDate: string;
  }, { rejectWithValue }) => {
    try {
      const response = await attendanceApi.getStudentAttendance(
        params.studentId,
        params.startDate,
        params.endDate
      );
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch student attendance');
    }
  }
);

export const fetchClassAttendance = createAsyncThunk(
  'attendance/fetchClassAttendance',
  async (params: { gradeId: string; date: string }, { rejectWithValue }) => {
    try {
      const response = await attendanceApi.getClassAttendance(params.gradeId, params.date);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch class attendance');
    }
  }
);

export const fetchAttendanceSummary = createAsyncThunk(
  'attendance/fetchSummary',
  async (params: {
    studentId: string;
    periodStart: string;
    periodEnd: string;
  }, { rejectWithValue }) => {
    try {
      const response = await attendanceApi.getAttendanceSummary(
        params.studentId,
        params.periodStart,
        params.periodEnd
      );
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch attendance summary');
    }
  }
);

export const markAttendance = createAsyncThunk(
  'attendance/markAttendance',
  async (data: {
    studentId: string;
    date: string;
    status: string;
    notes?: string;
  }, { rejectWithValue }) => {
    try {
      const response = await attendanceApi.markAttendance(
        data.studentId,
        data.date,
        data.status,
        data.notes
      );
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to mark attendance');
    }
  }
);

export const markBulkAttendance = createAsyncThunk(
  'attendance/markBulkAttendance',
  async (data: {
    gradeId: string;
    date: string;
    attendances: Array<{
      studentId: string;
      status: string;
      notes?: string;
    }>;
  }, { rejectWithValue }) => {
    try {
      const response = await attendanceApi.markBulkAttendance(data);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to mark bulk attendance');
    }
  }
);

export const requestLeave = createAsyncThunk(
  'attendance/requestLeave',
  async (leaveData: any, { rejectWithValue }) => {
    try {
      const response = await attendanceApi.requestLeave(leaveData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to request leave');
    }
  }
);

export const approveLeave = createAsyncThunk(
  'attendance/approveLeave',
  async (data: {
    leaveId: string;
    approvedBy: string;
    comments?: string;
  }, { rejectWithValue }) => {
    try {
      const response = await attendanceApi.approveLeave(
        data.leaveId,
        data.approvedBy,
        data.comments
      );
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to approve leave');
    }
  }
);

const attendanceSlice = createSlice({
  name: 'attendance',
  initialState,
  reducers: {
    setSelectedDate: (state, action: PayloadAction<string>) => {
      state.selectedDate = action.payload;
    },
    setSelectedGrade: (state, action: PayloadAction<string | null>) => {
      state.selectedGrade = action.payload;
    },
    setCurrentAttendance: (state, action: PayloadAction<StudentAttendance | null>) => {
      state.currentAttendance = action.payload;
    },
    setFilters: (state, action: PayloadAction<AttendanceFilters>) => {
      state.filters = action.payload;
      state.currentPage = 1;
    },
    updateFilter: (state, action: PayloadAction<{ key: keyof AttendanceFilters; value: any }>) => {
      state.filters[action.payload.key] = action.payload.value;
      state.currentPage = 1;
    },
    clearFilters: (state) => {
      state.filters = {};
      state.currentPage = 1;
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.currentPage = 1;
    },
    setSorting: (state, action: PayloadAction<{ sortBy: string; sortOrder: 'asc' | 'desc' }>) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetAttendanceState: (state) => {
      state.currentAttendance = null;
      state.currentSummary = null;
      state.filters = {};
      state.currentPage = 1;
      state.error = null;
    },
    updateAttendanceStatus: (state, action: PayloadAction<{
      studentId: string;
      date: string;
      status: string;
    }>) => {
      const attendance = state.attendances.find(
        a => a.studentId === action.payload.studentId && a.date === action.payload.date
      );
      if (attendance) {
        attendance.status = action.payload.status as any;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Student Attendance
      .addCase(fetchStudentAttendance.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchStudentAttendance.fulfilled, (state, action) => {
        state.isLoading = false;
        state.attendances = action.payload;
        state.lastUpdated = Date.now();
      })
      .addCase(fetchStudentAttendance.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch Class Attendance
      .addCase(fetchClassAttendance.fulfilled, (state, action) => {
        state.attendances = action.payload;
        state.lastUpdated = Date.now();
      })
      
      // Fetch Attendance Summary
      .addCase(fetchAttendanceSummary.fulfilled, (state, action) => {
        state.currentSummary = action.payload;
      })
      
      // Mark Attendance
      .addCase(markAttendance.pending, (state) => {
        state.isMarking = true;
        state.error = null;
      })
      .addCase(markAttendance.fulfilled, (state, action) => {
        state.isMarking = false;
        const index = state.attendances.findIndex(
          a => a.studentId === action.payload.studentId && a.date === action.payload.date
        );
        if (index !== -1) {
          state.attendances[index] = action.payload;
        } else {
          state.attendances.push(action.payload);
        }
      })
      .addCase(markAttendance.rejected, (state, action) => {
        state.isMarking = false;
        state.error = action.payload as string;
      })
      
      // Mark Bulk Attendance
      .addCase(markBulkAttendance.fulfilled, (state, action) => {
        action.payload.forEach((attendance: StudentAttendance) => {
          const index = state.attendances.findIndex(
            a => a.studentId === attendance.studentId && a.date === attendance.date
          );
          if (index !== -1) {
            state.attendances[index] = attendance;
          } else {
            state.attendances.push(attendance);
          }
        });
      })
      
      // Request Leave
      .addCase(requestLeave.fulfilled, (state, action) => {
        state.leaves.unshift(action.payload);
      })
      
      // Approve Leave
      .addCase(approveLeave.fulfilled, (state, action) => {
        const index = state.leaves.findIndex(l => l.id === action.payload.id);
        if (index !== -1) {
          state.leaves[index] = action.payload;
        }
      });
  },
});

export const {
  setSelectedDate,
  setSelectedGrade,
  setCurrentAttendance,
  setFilters,
  updateFilter,
  clearFilters,
  setPage,
  setPageSize,
  setSorting,
  clearError,
  resetAttendanceState,
  updateAttendanceStatus,
} = attendanceSlice.actions;

export default attendanceSlice.reducer;
