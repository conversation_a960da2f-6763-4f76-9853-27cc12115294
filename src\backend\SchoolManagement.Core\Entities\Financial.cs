using System.ComponentModel.DataAnnotations;

namespace SchoolManagement.Core.Entities;

/// <summary>
/// Fee structure for different grades and services
/// </summary>
public class FeeStructure : TenantEntity
{
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    [Required]
    public Guid AcademicYearId { get; set; }
    
    public Guid? GradeId { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string FeeType { get; set; } = string.Empty; // Tuition, Transport, Books, etc.
    
    [Required]
    public decimal Amount { get; set; }
    
    [MaxLength(20)]
    public string Currency { get; set; } = "USD";
    
    [Required]
    [MaxLength(50)]
    public string PaymentFrequency { get; set; } = string.Empty; // Annual, Semester, Monthly, etc.
    
    public DateTime? DueDate { get; set; }
    
    public bool IsOptional { get; set; } = false;
    
    public bool IsActive { get; set; } = true;
    
    public decimal? DiscountPercentage { get; set; }
    
    public decimal? LateFeeAmount { get; set; }
    
    public int? LateFeeGraceDays { get; set; }
    
    // Navigation properties
    public virtual AcademicYear AcademicYear { get; set; } = null!;
    public virtual Grade? Grade { get; set; }
    public virtual ICollection<StudentFee> StudentFees { get; set; } = new List<StudentFee>();
}

/// <summary>
/// Individual student fees based on fee structure
/// </summary>
public class StudentFee : TenantEntity
{
    [Required]
    public Guid StudentId { get; set; }
    
    [Required]
    public Guid FeeStructureId { get; set; }
    
    [Required]
    public decimal Amount { get; set; }
    
    [MaxLength(20)]
    public string Currency { get; set; } = "USD";
    
    public DateTime DueDate { get; set; }
    
    public decimal PaidAmount { get; set; } = 0;
    
    public decimal RemainingAmount => Amount - PaidAmount;
    
    public bool IsFullyPaid => PaidAmount >= Amount;
    
    public FeeStatus Status { get; set; } = FeeStatus.Pending;
    
    public decimal? DiscountAmount { get; set; }
    
    public decimal? LateFeeAmount { get; set; }
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    // Navigation properties
    public virtual Student Student { get; set; } = null!;
    public virtual FeeStructure FeeStructure { get; set; } = null!;
    public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
}

/// <summary>
/// Fee status enumeration
/// </summary>
public enum FeeStatus
{
    Pending,
    PartiallyPaid,
    FullyPaid,
    Overdue,
    Waived,
    Cancelled
}

/// <summary>
/// Payment records
/// </summary>
public class Payment : TenantEntity
{
    [Required]
    public Guid StudentId { get; set; }
    
    public Guid? StudentFeeId { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string PaymentNumber { get; set; } = string.Empty;
    
    [Required]
    public decimal Amount { get; set; }
    
    [MaxLength(20)]
    public string Currency { get; set; } = "USD";
    
    [Required]
    public DateTime PaymentDate { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string PaymentMethod { get; set; } = string.Empty; // Cash, Card, Bank Transfer, etc.
    
    [MaxLength(100)]
    public string? TransactionReference { get; set; }
    
    [MaxLength(100)]
    public string? BankReference { get; set; }
    
    public PaymentStatus Status { get; set; } = PaymentStatus.Completed;
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    public string? ReceivedBy { get; set; }
    
    /// <summary>
    /// Payment gateway response stored as JSON
    /// </summary>
    public string? PaymentGatewayResponse { get; set; }
    
    /// <summary>
    /// Receipt file path
    /// </summary>
    public string? ReceiptPath { get; set; }
    
    // Navigation properties
    public virtual Student Student { get; set; } = null!;
    public virtual StudentFee? StudentFee { get; set; }
    public virtual ICollection<PaymentAllocation> PaymentAllocations { get; set; } = new List<PaymentAllocation>();
}

/// <summary>
/// Payment status enumeration
/// </summary>
public enum PaymentStatus
{
    Pending,
    Completed,
    Failed,
    Cancelled,
    Refunded,
    PartiallyRefunded
}

/// <summary>
/// Payment allocation to specific fees
/// </summary>
public class PaymentAllocation : TenantEntity
{
    [Required]
    public Guid PaymentId { get; set; }
    
    [Required]
    public Guid StudentFeeId { get; set; }
    
    [Required]
    public decimal Amount { get; set; }
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    // Navigation properties
    public virtual Payment Payment { get; set; } = null!;
    public virtual StudentFee StudentFee { get; set; } = null!;
}

/// <summary>
/// Financial discounts and scholarships
/// </summary>
public class Discount : TenantEntity
{
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string Type { get; set; } = string.Empty; // Percentage, Fixed Amount, Scholarship
    
    public decimal Value { get; set; }
    
    public bool IsPercentage { get; set; } = true;
    
    public DateTime? ValidFrom { get; set; }
    
    public DateTime? ValidTo { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    [MaxLength(500)]
    public string? Conditions { get; set; }
    
    public int? MaxUsage { get; set; }
    
    public int CurrentUsage { get; set; } = 0;
    
    // Navigation properties
    public virtual ICollection<StudentDiscount> StudentDiscounts { get; set; } = new List<StudentDiscount>();
}

/// <summary>
/// Student-specific discounts
/// </summary>
public class StudentDiscount : TenantEntity
{
    [Required]
    public Guid StudentId { get; set; }
    
    [Required]
    public Guid DiscountId { get; set; }
    
    public DateTime AppliedDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? ExpiryDate { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    public string? ApprovedBy { get; set; }
    
    // Navigation properties
    public virtual Student Student { get; set; } = null!;
    public virtual Discount Discount { get; set; } = null!;
}

/// <summary>
/// Financial reports and analytics
/// </summary>
public class FinancialReport : TenantEntity
{
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(50)]
    public string Type { get; set; } = string.Empty; // Income, Expense, Balance Sheet, etc.
    
    [Required]
    public DateTime PeriodStart { get; set; }
    
    [Required]
    public DateTime PeriodEnd { get; set; }
    
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    
    public string? GeneratedBy { get; set; }
    
    /// <summary>
    /// Report data stored as JSON
    /// </summary>
    public string? ReportData { get; set; }
    
    /// <summary>
    /// Report file path (PDF, Excel, etc.)
    /// </summary>
    public string? FilePath { get; set; }
    
    public ReportStatus Status { get; set; } = ReportStatus.Generated;
    
    [MaxLength(500)]
    public string? Notes { get; set; }
}

/// <summary>
/// Report status enumeration
/// </summary>
public enum ReportStatus
{
    Generated,
    Approved,
    Published,
    Archived
}

/// <summary>
/// Expense tracking
/// </summary>
public class Expense : TenantEntity
{
    [Required]
    [MaxLength(100)]
    public string Description { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(50)]
    public string Category { get; set; } = string.Empty;
    
    [Required]
    public decimal Amount { get; set; }
    
    [MaxLength(20)]
    public string Currency { get; set; } = "USD";
    
    [Required]
    public DateTime ExpenseDate { get; set; }
    
    [MaxLength(100)]
    public string? Vendor { get; set; }
    
    [MaxLength(100)]
    public string? InvoiceNumber { get; set; }
    
    [MaxLength(50)]
    public string? PaymentMethod { get; set; }
    
    public ExpenseStatus Status { get; set; } = ExpenseStatus.Pending;
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    public string? ApprovedBy { get; set; }
    
    public DateTime? ApprovedAt { get; set; }
    
    /// <summary>
    /// Receipt/invoice file path
    /// </summary>
    public string? ReceiptPath { get; set; }
}

/// <summary>
/// Expense status enumeration
/// </summary>
public enum ExpenseStatus
{
    Pending,
    Approved,
    Paid,
    Rejected,
    Cancelled
}

/// <summary>
/// Invoice generation for Egypt/Saudi compliance
/// </summary>
public class Invoice : TenantEntity
{
    [Required]
    [MaxLength(50)]
    public string InvoiceNumber { get; set; } = string.Empty;
    
    [Required]
    public Guid StudentId { get; set; }
    
    [Required]
    public DateTime InvoiceDate { get; set; }
    
    public DateTime? DueDate { get; set; }
    
    [Required]
    public decimal SubTotal { get; set; }
    
    public decimal TaxAmount { get; set; } = 0;
    
    public decimal DiscountAmount { get; set; } = 0;
    
    [Required]
    public decimal TotalAmount { get; set; }
    
    [MaxLength(20)]
    public string Currency { get; set; } = "USD";
    
    public InvoiceStatus Status { get; set; } = InvoiceStatus.Draft;
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    /// <summary>
    /// Tax registration number for compliance
    /// </summary>
    [MaxLength(50)]
    public string? TaxRegistrationNumber { get; set; }
    
    /// <summary>
    /// E-invoice reference for Egypt/Saudi compliance
    /// </summary>
    [MaxLength(100)]
    public string? EInvoiceReference { get; set; }
    
    /// <summary>
    /// QR code for invoice verification
    /// </summary>
    public string? QRCode { get; set; }
    
    /// <summary>
    /// Invoice file path (PDF)
    /// </summary>
    public string? FilePath { get; set; }
    
    // Navigation properties
    public virtual Student Student { get; set; } = null!;
    public virtual ICollection<InvoiceItem> InvoiceItems { get; set; } = new List<InvoiceItem>();
}

/// <summary>
/// Invoice status enumeration
/// </summary>
public enum InvoiceStatus
{
    Draft,
    Sent,
    Paid,
    Overdue,
    Cancelled,
    Refunded
}

/// <summary>
/// Invoice line items
/// </summary>
public class InvoiceItem : TenantEntity
{
    [Required]
    public Guid InvoiceId { get; set; }
    
    public Guid? StudentFeeId { get; set; }
    
    [Required]
    [MaxLength(200)]
    public string Description { get; set; } = string.Empty;
    
    [Required]
    public int Quantity { get; set; } = 1;
    
    [Required]
    public decimal UnitPrice { get; set; }
    
    public decimal DiscountAmount { get; set; } = 0;
    
    public decimal TaxAmount { get; set; } = 0;
    
    [Required]
    public decimal TotalAmount { get; set; }
    
    // Navigation properties
    public virtual Invoice Invoice { get; set; } = null!;
    public virtual StudentFee? StudentFee { get; set; }
}
