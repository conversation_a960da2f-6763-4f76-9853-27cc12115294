using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SchoolManagement.Application.DTOs.Student;
using SchoolManagement.Application.Interfaces;
using SchoolManagement.Core.Interfaces;

namespace SchoolManagement.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class StudentsController : ControllerBase
{
    private readonly IStudentService _studentService;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<StudentsController> _logger;

    public StudentsController(
        IStudentService studentService,
        ICurrentUserService currentUserService,
        ILogger<StudentsController> logger)
    {
        _studentService = studentService;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<PagedResultDto<StudentDto>>> GetStudents(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? search = null,
        [FromQuery] string? grade = null,
        [FromQuery] string? status = null,
        [FromQuery] string? gender = null,
        [FromQuery] string? sortBy = "lastName",
        [FromQuery] string? sortOrder = "asc")
    {
        try
        {
            var result = await _studentService.GetStudentsAsync(
                page, pageSize, search, grade, status, gender, sortBy, sortOrder);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting students");
            return StatusCode(500, new { message = "An error occurred while getting students" });
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<StudentDetailDto>> GetStudent(Guid id)
    {
        try
        {
            var student = await _studentService.GetStudentByIdAsync(id);
            if (student == null)
            {
                return NotFound(new { message = "Student not found" });
            }

            return Ok(student);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting student: {StudentId}", id);
            return StatusCode(500, new { message = "An error occurred while getting student" });
        }
    }

    [HttpGet("by-number/{studentNumber}")]
    public async Task<ActionResult<StudentDetailDto>> GetStudentByNumber(string studentNumber)
    {
        try
        {
            var student = await _studentService.GetStudentByNumberAsync(studentNumber);
            if (student == null)
            {
                return NotFound(new { message = "Student not found" });
            }

            return Ok(student);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting student by number: {StudentNumber}", studentNumber);
            return StatusCode(500, new { message = "An error occurred while getting student" });
        }
    }

    [HttpPost]
    [Authorize(Roles = "Admin,TenantAdmin,Staff")]
    public async Task<ActionResult<StudentDto>> CreateStudent([FromBody] CreateStudentRequestDto request)
    {
        try
        {
            var student = await _studentService.CreateStudentAsync(request);
            return CreatedAtAction(nameof(GetStudent), new { id = student.Id }, student);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning("Failed to create student. Reason: {Reason}", ex.Message);
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating student");
            return StatusCode(500, new { message = "An error occurred while creating student" });
        }
    }

    [HttpPut("{id}")]
    [Authorize(Roles = "Admin,TenantAdmin,Staff")]
    public async Task<ActionResult<StudentDto>> UpdateStudent(Guid id, [FromBody] UpdateStudentRequestDto request)
    {
        try
        {
            var student = await _studentService.UpdateStudentAsync(id, request);
            return Ok(student);
        }
        catch (KeyNotFoundException)
        {
            return NotFound(new { message = "Student not found" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating student: {StudentId}", id);
            return StatusCode(500, new { message = "An error occurred while updating student" });
        }
    }

    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin,TenantAdmin")]
    public async Task<ActionResult> DeleteStudent(Guid id)
    {
        try
        {
            await _studentService.DeleteStudentAsync(id);
            return NoContent();
        }
        catch (KeyNotFoundException)
        {
            return NotFound(new { message = "Student not found" });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning("Failed to delete student {StudentId}. Reason: {Reason}", id, ex.Message);
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting student: {StudentId}", id);
            return StatusCode(500, new { message = "An error occurred while deleting student" });
        }
    }

    [HttpGet("by-grade/{gradeId}")]
    public async Task<ActionResult<IEnumerable<StudentDto>>> GetStudentsByGrade(Guid gradeId)
    {
        try
        {
            var students = await _studentService.GetStudentsByGradeAsync(gradeId);
            return Ok(students);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting students by grade: {GradeId}", gradeId);
            return StatusCode(500, new { message = "An error occurred while getting students" });
        }
    }

    [HttpPost("enroll")]
    [Authorize(Roles = "Admin,TenantAdmin,Staff")]
    public async Task<ActionResult<StudentEnrollmentDto>> EnrollStudent([FromBody] EnrollStudentRequestDto request)
    {
        try
        {
            var enrollment = await _studentService.EnrollStudentAsync(request);
            return Ok(enrollment);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning("Failed to enroll student. Reason: {Reason}", ex.Message);
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enrolling student");
            return StatusCode(500, new { message = "An error occurred while enrolling student" });
        }
    }

    [HttpGet("{id}/enrollments")]
    public async Task<ActionResult<IEnumerable<StudentEnrollmentDto>>> GetStudentEnrollments(Guid id)
    {
        try
        {
            var enrollments = await _studentService.GetStudentEnrollmentsAsync(id);
            return Ok(enrollments);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting student enrollments: {StudentId}", id);
            return StatusCode(500, new { message = "An error occurred while getting enrollments" });
        }
    }

    [HttpPost("enrollments/{enrollmentId}/withdraw")]
    [Authorize(Roles = "Admin,TenantAdmin,Staff")]
    public async Task<ActionResult> WithdrawStudent(Guid enrollmentId, [FromBody] WithdrawStudentRequestDto request)
    {
        try
        {
            await _studentService.WithdrawStudentAsync(enrollmentId, request.Reason);
            return Ok(new { message = "Student withdrawn successfully" });
        }
        catch (KeyNotFoundException)
        {
            return NotFound(new { message = "Enrollment not found" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error withdrawing student: {EnrollmentId}", enrollmentId);
            return StatusCode(500, new { message = "An error occurred while withdrawing student" });
        }
    }

    [HttpGet("{id}/guardians")]
    public async Task<ActionResult<IEnumerable<GuardianDto>>> GetStudentGuardians(Guid id)
    {
        try
        {
            var guardians = await _studentService.GetStudentGuardiansAsync(id);
            return Ok(guardians);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting student guardians: {StudentId}", id);
            return StatusCode(500, new { message = "An error occurred while getting guardians" });
        }
    }

    [HttpPost("{id}/guardians")]
    [Authorize(Roles = "Admin,TenantAdmin,Staff")]
    public async Task<ActionResult<GuardianDto>> AddGuardianToStudent(Guid id, [FromBody] CreateGuardianRequestDto request)
    {
        try
        {
            var guardian = await _studentService.AddGuardianToStudentAsync(id, request);
            return Ok(guardian);
        }
        catch (KeyNotFoundException)
        {
            return NotFound(new { message = "Student not found" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding guardian to student: {StudentId}", id);
            return StatusCode(500, new { message = "An error occurred while adding guardian" });
        }
    }

    [HttpDelete("{studentId}/guardians/{guardianId}")]
    [Authorize(Roles = "Admin,TenantAdmin,Staff")]
    public async Task<ActionResult> RemoveGuardianFromStudent(Guid studentId, Guid guardianId)
    {
        try
        {
            await _studentService.RemoveGuardianFromStudentAsync(studentId, guardianId);
            return NoContent();
        }
        catch (KeyNotFoundException)
        {
            return NotFound(new { message = "Student or guardian not found" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing guardian from student: {StudentId}, {GuardianId}", 
                studentId, guardianId);
            return StatusCode(500, new { message = "An error occurred while removing guardian" });
        }
    }

    [HttpPost("{id}/profile-picture")]
    [Authorize(Roles = "Admin,TenantAdmin,Staff")]
    public async Task<ActionResult<UploadResultDto>> UploadProfilePicture(Guid id, IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { message = "No file provided" });
            }

            var result = await _studentService.UploadProfilePictureAsync(id, file);
            return Ok(result);
        }
        catch (KeyNotFoundException)
        {
            return NotFound(new { message = "Student not found" });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading profile picture for student: {StudentId}", id);
            return StatusCode(500, new { message = "An error occurred while uploading profile picture" });
        }
    }

    [HttpGet("{id}/documents")]
    public async Task<ActionResult<IEnumerable<StudentDocumentDto>>> GetStudentDocuments(Guid id)
    {
        try
        {
            var documents = await _studentService.GetStudentDocumentsAsync(id);
            return Ok(documents);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting student documents: {StudentId}", id);
            return StatusCode(500, new { message = "An error occurred while getting documents" });
        }
    }

    [HttpPost("{id}/documents")]
    [Authorize(Roles = "Admin,TenantAdmin,Staff")]
    public async Task<ActionResult<StudentDocumentDto>> UploadStudentDocument(
        Guid id, 
        IFormFile file, 
        [FromForm] string documentType, 
        [FromForm] string? description = null)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { message = "No file provided" });
            }

            var document = await _studentService.UploadStudentDocumentAsync(id, file, documentType, description);
            return Ok(document);
        }
        catch (KeyNotFoundException)
        {
            return NotFound(new { message = "Student not found" });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading document for student: {StudentId}", id);
            return StatusCode(500, new { message = "An error occurred while uploading document" });
        }
    }

    [HttpDelete("{studentId}/documents/{documentId}")]
    [Authorize(Roles = "Admin,TenantAdmin,Staff")]
    public async Task<ActionResult> DeleteStudentDocument(Guid studentId, Guid documentId)
    {
        try
        {
            await _studentService.DeleteStudentDocumentAsync(studentId, documentId);
            return NoContent();
        }
        catch (KeyNotFoundException)
        {
            return NotFound(new { message = "Student or document not found" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting document: {StudentId}, {DocumentId}", studentId, documentId);
            return StatusCode(500, new { message = "An error occurred while deleting document" });
        }
    }

    [HttpGet("export")]
    [Authorize(Roles = "Admin,TenantAdmin,Staff")]
    public async Task<ActionResult> ExportStudents(
        [FromQuery] string format = "excel",
        [FromQuery] string? search = null,
        [FromQuery] string? grade = null,
        [FromQuery] string? status = null)
    {
        try
        {
            var result = await _studentService.ExportStudentsAsync(format, search, grade, status);
            return File(result.Data, result.ContentType, result.FileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting students");
            return StatusCode(500, new { message = "An error occurred while exporting students" });
        }
    }

    [HttpPost("import")]
    [Authorize(Roles = "Admin,TenantAdmin")]
    public async Task<ActionResult<ImportResultDto>> ImportStudents(IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { message = "No file provided" });
            }

            var result = await _studentService.ImportStudentsAsync(file);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing students");
            return StatusCode(500, new { message = "An error occurred while importing students" });
        }
    }
}
