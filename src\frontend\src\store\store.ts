import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

import authSlice from './slices/authSlice';
import tenantSlice from './slices/tenantSlice';
import uiSlice from './slices/uiSlice';
import studentSlice from './slices/studentSlice';
import staffSlice from './slices/staffSlice';
import academicSlice from './slices/academicSlice';
import financeSlice from './slices/financeSlice';
import attendanceSlice from './slices/attendanceSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    tenant: tenantSlice,
    ui: uiSlice,
    students: studentSlice,
    staff: staffSlice,
    academics: academicSlice,
    finance: financeSlice,
    attendance: attendanceSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Typed hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
