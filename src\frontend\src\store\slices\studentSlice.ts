import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { studentApi } from '../../services/api/studentApi';

export interface Student {
  id: string;
  studentNumber: string;
  userId: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  fullName: string;
  displayName: string;
  arabicFirstName?: string;
  arabicLastName?: string;
  arabicMiddleName?: string;
  arabicFullName?: string;
  dateOfBirth: string;
  age: number;
  gender: string;
  nationality?: string;
  nationalId?: string;
  passportNumber?: string;
  placeOfBirth?: string;
  bloodType?: string;
  emergencyContactPhone?: string;
  emergencyContactName?: string;
  emergencyContactRelation?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  enrollmentDate: string;
  graduationDate?: string;
  status: 'Active' | 'Inactive' | 'Graduated' | 'Transferred' | 'Suspended' | 'Expelled' | 'Withdrawn';
  notes?: string;
  profilePictureUrl?: string;
  medicalInfo?: any;
  specialNeeds?: string;
  transportationInfo?: string;
  email: string;
  phoneNumber?: string;
  guardians?: Guardian[];
  enrollments?: StudentEnrollment[];
  createdAt: string;
  updatedAt?: string;
}

export interface Guardian {
  id: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  fullName: string;
  displayName: string;
  email?: string;
  phoneNumber?: string;
  workPhone?: string;
  occupation?: string;
  employer?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  nationalId?: string;
  dateOfBirth?: string;
  gender?: string;
  relationship: string;
  isPrimaryContact: boolean;
  isEmergencyContact: boolean;
  canPickupStudent: boolean;
  receiveReports: boolean;
  receiveNotifications: boolean;
}

export interface StudentEnrollment {
  id: string;
  studentId: string;
  gradeId: string;
  gradeName: string;
  academicYearId: string;
  academicYearName: string;
  enrollmentDate: string;
  withdrawalDate?: string;
  status: 'Active' | 'Withdrawn' | 'Transferred' | 'Completed' | 'Suspended';
  notes?: string;
}

export interface StudentFilters {
  search?: string;
  grade?: string;
  status?: string;
  gender?: string;
  nationality?: string;
  enrollmentYear?: string;
  age?: { min?: number; max?: number };
}

export interface StudentState {
  students: Student[];
  currentStudent: Student | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
  filters: StudentFilters;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: string | null;
  lastUpdated: number | null;
}

const initialState: StudentState = {
  students: [],
  currentStudent: null,
  totalCount: 0,
  currentPage: 1,
  pageSize: 20,
  filters: {},
  sortBy: 'lastName',
  sortOrder: 'asc',
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
  lastUpdated: null,
};

// Async thunks
export const fetchStudents = createAsyncThunk(
  'students/fetchStudents',
  async (params: {
    page?: number;
    pageSize?: number;
    filters?: StudentFilters;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}, { rejectWithValue }) => {
    try {
      const response = await studentApi.getStudents(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch students');
    }
  }
);

export const fetchStudentById = createAsyncThunk(
  'students/fetchById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await studentApi.getStudentById(id);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch student');
    }
  }
);

export const createStudent = createAsyncThunk(
  'students/create',
  async (studentData: any, { rejectWithValue }) => {
    try {
      const response = await studentApi.createStudent(studentData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create student');
    }
  }
);

export const updateStudent = createAsyncThunk(
  'students/update',
  async ({ id, data }: { id: string; data: any }, { rejectWithValue }) => {
    try {
      const response = await studentApi.updateStudent(id, data);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update student');
    }
  }
);

export const deleteStudent = createAsyncThunk(
  'students/delete',
  async (id: string, { rejectWithValue }) => {
    try {
      await studentApi.deleteStudent(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete student');
    }
  }
);

export const enrollStudent = createAsyncThunk(
  'students/enroll',
  async (enrollmentData: {
    studentId: string;
    gradeId: string;
    academicYearId: string;
  }, { rejectWithValue }) => {
    try {
      const response = await studentApi.enrollStudent(enrollmentData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to enroll student');
    }
  }
);

const studentSlice = createSlice({
  name: 'students',
  initialState,
  reducers: {
    setCurrentStudent: (state, action: PayloadAction<Student | null>) => {
      state.currentStudent = action.payload;
    },
    setFilters: (state, action: PayloadAction<StudentFilters>) => {
      state.filters = action.payload;
      state.currentPage = 1; // Reset to first page when filters change
    },
    updateFilter: (state, action: PayloadAction<{ key: keyof StudentFilters; value: any }>) => {
      state.filters[action.payload.key] = action.payload.value;
      state.currentPage = 1;
    },
    clearFilters: (state) => {
      state.filters = {};
      state.currentPage = 1;
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.currentPage = 1;
    },
    setSorting: (state, action: PayloadAction<{ sortBy: string; sortOrder: 'asc' | 'desc' }>) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetStudentState: (state) => {
      state.currentStudent = null;
      state.filters = {};
      state.currentPage = 1;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Students
      .addCase(fetchStudents.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchStudents.fulfilled, (state, action) => {
        state.isLoading = false;
        state.students = action.payload.items;
        state.totalCount = action.payload.totalCount;
        state.currentPage = action.payload.page;
        state.pageSize = action.payload.pageSize;
        state.lastUpdated = Date.now();
      })
      .addCase(fetchStudents.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch Student by ID
      .addCase(fetchStudentById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchStudentById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentStudent = action.payload;
      })
      .addCase(fetchStudentById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Create Student
      .addCase(createStudent.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createStudent.fulfilled, (state, action) => {
        state.isCreating = false;
        state.students.unshift(action.payload);
        state.totalCount += 1;
        state.currentStudent = action.payload;
      })
      .addCase(createStudent.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.payload as string;
      })
      
      // Update Student
      .addCase(updateStudent.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateStudent.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.students.findIndex(s => s.id === action.payload.id);
        if (index !== -1) {
          state.students[index] = action.payload;
        }
        if (state.currentStudent?.id === action.payload.id) {
          state.currentStudent = action.payload;
        }
      })
      .addCase(updateStudent.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      })
      
      // Delete Student
      .addCase(deleteStudent.pending, (state) => {
        state.isDeleting = true;
        state.error = null;
      })
      .addCase(deleteStudent.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.students = state.students.filter(s => s.id !== action.payload);
        state.totalCount = Math.max(0, state.totalCount - 1);
        if (state.currentStudent?.id === action.payload) {
          state.currentStudent = null;
        }
      })
      .addCase(deleteStudent.rejected, (state, action) => {
        state.isDeleting = false;
        state.error = action.payload as string;
      })
      
      // Enroll Student
      .addCase(enrollStudent.fulfilled, (state, action) => {
        if (state.currentStudent) {
          state.currentStudent.enrollments = state.currentStudent.enrollments || [];
          state.currentStudent.enrollments.push(action.payload);
        }
      });
  },
});

export const {
  setCurrentStudent,
  setFilters,
  updateFilter,
  clearFilters,
  setPage,
  setPageSize,
  setSorting,
  clearError,
  resetStudentState,
} = studentSlice.actions;

export default studentSlice.reducer;
