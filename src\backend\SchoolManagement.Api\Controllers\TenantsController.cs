using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AutoMapper;
using SchoolManagement.Core.Interfaces;
using SchoolManagement.Core.Entities;
using SchoolManagement.Application.DTOs;

namespace SchoolManagement.Api.Controllers;

/// <summary>
/// Controller for tenant management operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class TenantsController : ControllerBase
{
    private readonly ITenantService _tenantService;
    private readonly IMapper _mapper;
    private readonly ILogger<TenantsController> _logger;

    public TenantsController(
        ITenantService tenantService,
        IMapper mapper,
        ILogger<TenantsController> logger)
    {
        _tenantService = tenantService;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// Get all tenants (System Admin only)
    /// </summary>
    /// <returns>List of all tenants</returns>
    [HttpGet]
    [Authorize(Roles = "SystemAdmin")]
    [ProducesResponseType(typeof(IEnumerable<TenantSummaryDto>), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<ActionResult<IEnumerable<TenantSummaryDto>>> GetAllTenants()
    {
        try
        {
            var tenants = await _tenantService.GetAllTenantsAsync();
            var tenantDtos = _mapper.Map<IEnumerable<TenantSummaryDto>>(tenants);
            
            return Ok(tenantDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all tenants");
            return StatusCode(500, "An error occurred while retrieving tenants");
        }
    }

    /// <summary>
    /// Get tenant by ID
    /// </summary>
    /// <param name="id">Tenant ID</param>
    /// <returns>Tenant details</returns>
    [HttpGet("{id:guid}")]
    [Authorize]
    [ProducesResponseType(typeof(TenantDto), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<TenantDto>> GetTenant(Guid id)
    {
        try
        {
            var tenant = await _tenantService.GetTenantByIdAsync(id);
            if (tenant == null)
            {
                return NotFound($"Tenant with ID {id} not found");
            }

            var tenantDto = _mapper.Map<TenantDto>(tenant);
            return Ok(tenantDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tenant {TenantId}", id);
            return StatusCode(500, "An error occurred while retrieving the tenant");
        }
    }

    /// <summary>
    /// Get tenant by code
    /// </summary>
    /// <param name="code">Tenant code</param>
    /// <returns>Tenant details</returns>
    [HttpGet("by-code/{code}")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(TenantDto), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<TenantDto>> GetTenantByCode(string code)
    {
        try
        {
            var tenant = await _tenantService.GetTenantByCodeAsync(code);
            if (tenant == null)
            {
                return NotFound($"Tenant with code '{code}' not found");
            }

            var tenantDto = _mapper.Map<TenantDto>(tenant);
            return Ok(tenantDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tenant by code {TenantCode}", code);
            return StatusCode(500, "An error occurred while retrieving the tenant");
        }
    }

    /// <summary>
    /// Create a new tenant (System Admin only)
    /// </summary>
    /// <param name="createTenantDto">Tenant creation data</param>
    /// <returns>Created tenant</returns>
    [HttpPost]
    [Authorize(Roles = "SystemAdmin")]
    [ProducesResponseType(typeof(TenantDto), 201)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(409)]
    public async Task<ActionResult<TenantDto>> CreateTenant([FromBody] CreateTenantDto createTenantDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var tenant = _mapper.Map<Tenant>(createTenantDto);
            var createdTenant = await _tenantService.CreateTenantAsync(tenant);
            var tenantDto = _mapper.Map<TenantDto>(createdTenant);

            return CreatedAtAction(
                nameof(GetTenant),
                new { id = createdTenant.Id },
                tenantDto);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Conflict while creating tenant");
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating tenant");
            return StatusCode(500, "An error occurred while creating the tenant");
        }
    }

    /// <summary>
    /// Update an existing tenant
    /// </summary>
    /// <param name="id">Tenant ID</param>
    /// <param name="updateTenantDto">Tenant update data</param>
    /// <returns>Updated tenant</returns>
    [HttpPut("{id:guid}")]
    [Authorize(Roles = "SystemAdmin,TenantAdmin")]
    [ProducesResponseType(typeof(TenantDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(404)]
    [ProducesResponseType(409)]
    public async Task<ActionResult<TenantDto>> UpdateTenant(Guid id, [FromBody] UpdateTenantDto updateTenantDto)
    {
        try
        {
            if (id != updateTenantDto.Id)
            {
                return BadRequest("Tenant ID in URL does not match the ID in the request body");
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var tenant = _mapper.Map<Tenant>(updateTenantDto);
            var updatedTenant = await _tenantService.UpdateTenantAsync(tenant);
            var tenantDto = _mapper.Map<TenantDto>(updatedTenant);

            return Ok(tenantDto);
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("not found"))
        {
            return NotFound(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Conflict while updating tenant {TenantId}", id);
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant {TenantId}", id);
            return StatusCode(500, "An error occurred while updating the tenant");
        }
    }

    /// <summary>
    /// Delete a tenant (System Admin only)
    /// </summary>
    /// <param name="id">Tenant ID</param>
    /// <returns>No content</returns>
    [HttpDelete("{id:guid}")]
    [Authorize(Roles = "SystemAdmin")]
    [ProducesResponseType(204)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(404)]
    public async Task<IActionResult> DeleteTenant(Guid id)
    {
        try
        {
            await _tenantService.DeleteTenantAsync(id);
            return NoContent();
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("not found"))
        {
            return NotFound(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Cannot delete tenant {TenantId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting tenant {TenantId}", id);
            return StatusCode(500, "An error occurred while deleting the tenant");
        }
    }

    /// <summary>
    /// Check if tenant is active
    /// </summary>
    /// <param name="id">Tenant ID</param>
    /// <returns>Tenant active status</returns>
    [HttpGet("{id:guid}/status")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> GetTenantStatus(Guid id)
    {
        try
        {
            var isActive = await _tenantService.IsTenantActiveAsync(id);
            var tenant = await _tenantService.GetTenantByIdAsync(id);
            
            if (tenant == null)
            {
                return NotFound($"Tenant with ID {id} not found");
            }

            return Ok(new
            {
                tenantId = id,
                isActive = isActive,
                name = tenant.Name,
                code = tenant.Code,
                subscriptionPlan = tenant.SubscriptionPlan,
                subscriptionEndDate = tenant.SubscriptionEndDate
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking tenant status {TenantId}", id);
            return StatusCode(500, "An error occurred while checking tenant status");
        }
    }

    /// <summary>
    /// Update tenant configuration
    /// </summary>
    /// <param name="id">Tenant ID</param>
    /// <param name="configDto">Configuration data</param>
    /// <returns>Updated configuration</returns>
    [HttpPut("{id:guid}/configuration")]
    [Authorize(Roles = "SystemAdmin,TenantAdmin")]
    [ProducesResponseType(typeof(TenantConfigurationDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<TenantConfigurationDto>> UpdateTenantConfiguration(
        Guid id, 
        [FromBody] TenantConfigurationDto configDto)
    {
        try
        {
            if (id != configDto.TenantId)
            {
                return BadRequest("Tenant ID in URL does not match the ID in the request body");
            }

            var tenant = await _tenantService.GetTenantByIdAsync(id);
            if (tenant == null)
            {
                return NotFound($"Tenant with ID {id} not found");
            }

            // Update configuration
            tenant.Settings = System.Text.Json.JsonSerializer.Serialize(configDto.Settings);
            tenant.BrandingConfig = System.Text.Json.JsonSerializer.Serialize(configDto.BrandingConfig);
            tenant.ComplianceConfig = System.Text.Json.JsonSerializer.Serialize(configDto.ComplianceConfig);

            var updatedTenant = await _tenantService.UpdateTenantAsync(tenant);
            var responseDto = _mapper.Map<TenantConfigurationDto>(updatedTenant);

            return Ok(responseDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant configuration {TenantId}", id);
            return StatusCode(500, "An error occurred while updating tenant configuration");
        }
    }
}
