import React, { useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box, Typography } from '@mui/material';
import { useAppDispatch } from '../../store/store';
import { setPageTitle, setBreadcrumbs } from '../../store/slices/uiSlice';

const StudentsListPage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setPageTitle('Students'));
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', path: '/dashboard' },
      { label: 'Students' }
    ]));
  }, [dispatch]);

  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 700, mb: 3 }}>
        Students Management
      </Typography>
      <Typography variant="body1" color="text.secondary">
        Student management functionality will be implemented here.
      </Typography>
    </Box>
  );
};

const StudentsPage: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<StudentsListPage />} />
      <Route path="/enrollment" element={<StudentsListPage />} />
      <Route path="/guardians" element={<StudentsListPage />} />
    </Routes>
  );
};

export default StudentsPage;
