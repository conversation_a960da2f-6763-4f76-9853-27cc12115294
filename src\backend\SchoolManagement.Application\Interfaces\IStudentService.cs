using SchoolManagement.Application.DTOs.Common;
using SchoolManagement.Application.DTOs.Student;
using Microsoft.AspNetCore.Http;

namespace SchoolManagement.Application.Interfaces;

public interface IStudentService
{
    Task<PagedResultDto<StudentDto>> GetStudentsAsync(
        int page = 1,
        int pageSize = 20,
        string? search = null,
        string? grade = null,
        string? status = null,
        string? gender = null,
        string? sortBy = "lastName",
        string? sortOrder = "asc");

    Task<StudentDetailDto?> GetStudentByIdAsync(Guid id);
    Task<StudentDetailDto?> GetStudentByNumberAsync(string studentNumber);
    Task<StudentDto> CreateStudentAsync(CreateStudentRequestDto request);
    Task<StudentDto> UpdateStudentAsync(Guid id, UpdateStudentRequestDto request);
    Task DeleteStudentAsync(Guid id);
    Task<IEnumerable<StudentDto>> GetStudentsByGradeAsync(Guid gradeId);
    Task<StudentEnrollmentDto> EnrollStudentAsync(EnrollStudentRequestDto request);
    Task<IEnumerable<StudentEnrollmentDto>> GetStudentEnrollmentsAsync(Guid studentId);
    Task WithdrawStudentAsync(Guid enrollmentId, string reason);
    Task<IEnumerable<GuardianDto>> GetStudentGuardiansAsync(Guid studentId);
    Task<GuardianDto> AddGuardianToStudentAsync(Guid studentId, CreateGuardianRequestDto request);
    Task RemoveGuardianFromStudentAsync(Guid studentId, Guid guardianId);
    Task<UploadResultDto> UploadProfilePictureAsync(Guid studentId, IFormFile file);
    Task<IEnumerable<StudentDocumentDto>> GetStudentDocumentsAsync(Guid studentId);
    Task<StudentDocumentDto> UploadStudentDocumentAsync(Guid studentId, IFormFile file, string documentType, string? description = null);
    Task DeleteStudentDocumentAsync(Guid studentId, Guid documentId);
    Task<ExportResultDto> ExportStudentsAsync(string format, string? search = null, string? grade = null, string? status = null);
    Task<ImportResultDto> ImportStudentsAsync(IFormFile file);
}
