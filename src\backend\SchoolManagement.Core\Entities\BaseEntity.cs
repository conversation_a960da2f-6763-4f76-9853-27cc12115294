using System.ComponentModel.DataAnnotations;

namespace SchoolManagement.Core.Entities;

/// <summary>
/// Base entity class that provides common properties for all entities
/// </summary>
public abstract class BaseEntity
{
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public string? CreatedBy { get; set; }
    
    public string? UpdatedBy { get; set; }
    
    public bool IsDeleted { get; set; } = false;
    
    public DateTime? DeletedAt { get; set; }
    
    public string? DeletedBy { get; set; }
}

/// <summary>
/// Base entity for multi-tenant entities
/// </summary>
public abstract class TenantEntity : BaseEntity
{
    [Required]
    public Guid TenantId { get; set; }
    
    /// <summary>
    /// Navigation property to Tenant
    /// </summary>
    public virtual Tenant? Tenant { get; set; }
}

/// <summary>
/// Audit entity for tracking changes
/// </summary>
public class AuditLog : BaseEntity
{
    [Required]
    public Guid TenantId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string EntityName { get; set; } = string.Empty;
    
    [Required]
    public Guid EntityId { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string Action { get; set; } = string.Empty; // CREATE, UPDATE, DELETE
    
    public string? OldValues { get; set; }
    
    public string? NewValues { get; set; }
    
    [Required]
    [MaxLength(256)]
    public string UserId { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? UserName { get; set; }
    
    [MaxLength(45)]
    public string? IpAddress { get; set; }
    
    [MaxLength(500)]
    public string? UserAgent { get; set; }
}
