{"format": 1, "restore": {"D:\\School Product\\src\\backend\\SchoolManagement.Api\\SchoolManagement.Api.csproj": {}}, "projects": {"D:\\School Product\\src\\backend\\SchoolManagement.Api\\SchoolManagement.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\School Product\\src\\backend\\SchoolManagement.Api\\SchoolManagement.Api.csproj", "projectName": "SchoolManagement.Api", "projectPath": "D:\\School Product\\src\\backend\\SchoolManagement.Api\\SchoolManagement.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\School Product\\src\\backend\\SchoolManagement.Api\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 21.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\DevExpress 24.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.lowcalories.ae/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\School Product\\src\\backend\\SchoolManagement.Application\\SchoolManagement.Application.csproj": {"projectPath": "D:\\School Product\\src\\backend\\SchoolManagement.Application\\SchoolManagement.Application.csproj"}, "D:\\School Product\\src\\backend\\SchoolManagement.Core\\SchoolManagement.Core.csproj": {"projectPath": "D:\\School Product\\src\\backend\\SchoolManagement.Core\\SchoolManagement.Core.csproj"}, "D:\\School Product\\src\\backend\\SchoolManagement.Infrastructure\\SchoolManagement.Infrastructure.csproj": {"projectPath": "D:\\School Product\\src\\backend\\SchoolManagement.Infrastructure\\SchoolManagement.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}, "MediatR": {"target": "Package", "version": "[12.2.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Cors": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.RateLimiting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\School Product\\src\\backend\\SchoolManagement.Application\\SchoolManagement.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\School Product\\src\\backend\\SchoolManagement.Application\\SchoolManagement.Application.csproj", "projectName": "SchoolManagement.Application", "projectPath": "D:\\School Product\\src\\backend\\SchoolManagement.Application\\SchoolManagement.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\School Product\\src\\backend\\SchoolManagement.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 21.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\DevExpress 24.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.lowcalories.ae/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\School Product\\src\\backend\\SchoolManagement.Core\\SchoolManagement.Core.csproj": {"projectPath": "D:\\School Product\\src\\backend\\SchoolManagement.Core\\SchoolManagement.Core.csproj"}, "D:\\School Product\\src\\backend\\SchoolManagement.Infrastructure\\SchoolManagement.Infrastructure.csproj": {"projectPath": "D:\\School Product\\src\\backend\\SchoolManagement.Infrastructure\\SchoolManagement.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.8.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.8.0, )"}, "MediatR": {"target": "Package", "version": "[12.2.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\School Product\\src\\backend\\SchoolManagement.Core\\SchoolManagement.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\School Product\\src\\backend\\SchoolManagement.Core\\SchoolManagement.Core.csproj", "projectName": "SchoolManagement.Core", "projectPath": "D:\\School Product\\src\\backend\\SchoolManagement.Core\\SchoolManagement.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\School Product\\src\\backend\\SchoolManagement.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 21.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\DevExpress 24.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.lowcalories.ae/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\School Product\\src\\backend\\SchoolManagement.Infrastructure\\SchoolManagement.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\School Product\\src\\backend\\SchoolManagement.Infrastructure\\SchoolManagement.Infrastructure.csproj", "projectName": "SchoolManagement.Infrastructure", "projectPath": "D:\\School Product\\src\\backend\\SchoolManagement.Infrastructure\\SchoolManagement.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\School Product\\src\\backend\\SchoolManagement.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files\\DevExpress 24.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 21.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\DevExpress 24.1\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.lowcalories.ae/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\School Product\\src\\backend\\SchoolManagement.Core\\SchoolManagement.Core.csproj": {"projectPath": "D:\\School Product\\src\\backend\\SchoolManagement.Core\\SchoolManagement.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Azure.Identity": {"target": "Package", "version": "[1.10.4, )"}, "Azure.Storage.Blobs": {"target": "Package", "version": "[12.19.1, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}