import React, { useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box, Typography } from '@mui/material';
import { useAppDispatch } from '../../store/store';
import { setPageTitle, setBreadcrumbs } from '../../store/slices/uiSlice';

const AcademicsListPage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setPageTitle('Academics'));
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', path: '/dashboard' },
      { label: 'Academics' }
    ]));
  }, [dispatch]);

  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 700, mb: 3 }}>
        Academic Management
      </Typography>
      <Typography variant="body1" color="text.secondary">
        Academic management functionality will be implemented here.
      </Typography>
    </Box>
  );
};

const AcademicsPage: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<AcademicsListPage />} />
      <Route path="/grades" element={<AcademicsListPage />} />
      <Route path="/subjects" element={<AcademicsListPage />} />
      <Route path="/schedule" element={<AcademicsListPage />} />
      <Route path="/exams" element={<AcademicsListPage />} />
    </Routes>
  );
};

export default AcademicsPage;
