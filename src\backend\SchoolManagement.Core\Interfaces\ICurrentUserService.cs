namespace SchoolManagement.Core.Interfaces;

/// <summary>
/// Interface for accessing current user information
/// </summary>
public interface ICurrentUserService
{
    /// <summary>
    /// Gets the current user ID
    /// </summary>
    string? UserId { get; }
    
    /// <summary>
    /// Gets the current user email
    /// </summary>
    string? UserEmail { get; }
    
    /// <summary>
    /// Gets the current user's full name
    /// </summary>
    string? UserName { get; }
    
    /// <summary>
    /// Gets the current user's roles
    /// </summary>
    IEnumerable<string> UserRoles { get; }
    
    /// <summary>
    /// Gets the current user's tenant ID
    /// </summary>
    Guid? TenantId { get; }
    
    /// <summary>
    /// Checks if the current user is authenticated
    /// </summary>
    bool IsAuthenticated { get; }
    
    /// <summary>
    /// Checks if the current user has a specific role
    /// </summary>
    bool IsInRole(string role);
    
    /// <summary>
    /// Checks if the current user has a specific permission
    /// </summary>
    bool HasPermission(string permission);
    
    /// <summary>
    /// Gets the current user's IP address
    /// </summary>
    string? IpAddress { get; }
    
    /// <summary>
    /// Gets the current user's user agent
    /// </summary>
    string? UserAgent { get; }
}
