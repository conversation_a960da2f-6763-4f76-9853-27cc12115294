namespace SchoolManagement.Core.Interfaces;

/// <summary>
/// Interface for tenant context management
/// </summary>
public interface ITenantContext
{
    /// <summary>
    /// Gets the current tenant ID
    /// </summary>
    Guid? TenantId { get; }
    
    /// <summary>
    /// Gets the current tenant information
    /// </summary>
    TenantInfo? Tenant { get; }
    
    /// <summary>
    /// Sets the current tenant
    /// </summary>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="tenantInfo">Tenant information</param>
    void SetTenant(Guid tenantId, TenantInfo? tenantInfo = null);
    
    /// <summary>
    /// Clears the current tenant context
    /// </summary>
    void ClearTenant();
    
    /// <summary>
    /// Checks if a tenant is set
    /// </summary>
    bool HasTenant { get; }
}

/// <summary>
/// Tenant information for context
/// </summary>
public class TenantInfo
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public string? DatabaseConnectionString { get; set; }
    public Dictionary<string, object>? Settings { get; set; }
    public Dictionary<string, object>? BrandingConfig { get; set; }
    public Dictionary<string, object>? ComplianceConfig { get; set; }
}
