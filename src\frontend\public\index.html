<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="School Management System - Comprehensive multi-tenant school administration platform"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <title>School Management System</title>
    
    <style>
      /* Loading screen styles */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-family: 'Inter', sans-serif;
      }
      
      .loading-logo {
        width: 80px;
        height: 80px;
        margin-bottom: 20px;
        border-radius: 16px;
        background: rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        font-weight: 600;
      }
      
      .loading-text {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 10px;
      }
      
      .loading-subtitle {
        font-size: 14px;
        opacity: 0.8;
        margin-bottom: 30px;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loading screen when app loads */
      .app-loaded #loading-screen {
        display: none;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    
    <!-- Loading Screen -->
    <div id="loading-screen">
      <div class="loading-logo">SMS</div>
      <div class="loading-text">School Management System</div>
      <div class="loading-subtitle">Loading your dashboard...</div>
      <div class="loading-spinner"></div>
    </div>
    
    <div id="root"></div>
    
    <script>
      // Hide loading screen when React app loads
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('app-loaded');
        }, 1000);
      });
    </script>
  </body>
</html>
