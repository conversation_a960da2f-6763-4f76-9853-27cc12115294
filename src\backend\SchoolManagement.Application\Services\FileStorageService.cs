using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SchoolManagement.Application.DTOs.Common;
using SchoolManagement.Application.Interfaces;

namespace SchoolManagement.Application.Services;

public class FileStorageService : IFileStorageService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<FileStorageService> _logger;
    private readonly string _basePath;
    private readonly long _maxFileSize;
    private readonly string[] _allowedExtensions;

    public FileStorageService(IConfiguration configuration, ILogger<FileStorageService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _basePath = _configuration["FileStorage:LocalPath"] ?? "wwwroot/uploads";
        _maxFileSize = long.Parse(_configuration["FileStorage:MaxFileSize"] ?? "10485760"); // 10MB default
        _allowedExtensions = _configuration.GetSection("FileStorage:AllowedExtensions").Get<string[]>() ?? 
            new[] { ".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx" };

        // Ensure upload directory exists
        if (!Directory.Exists(_basePath))
        {
            Directory.CreateDirectory(_basePath);
        }
    }

    public async Task<UploadResultDto> UploadFileAsync(IFormFile file, string folder, string? fileName = null)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                throw new ArgumentException("File is empty or null");
            }

            if (!IsValidFileType(file.FileName))
            {
                throw new InvalidOperationException($"File type not allowed. Allowed types: {string.Join(", ", _allowedExtensions)}");
            }

            if (!IsValidFileSize(file.Length))
            {
                throw new InvalidOperationException($"File size exceeds maximum allowed size of {_maxFileSize / 1024 / 1024}MB");
            }

            // Generate unique filename if not provided
            var originalFileName = Path.GetFileNameWithoutExtension(file.FileName);
            var extension = Path.GetExtension(file.FileName);
            var uniqueFileName = fileName ?? $"{originalFileName}_{Guid.NewGuid()}{extension}";

            // Create folder path
            var folderPath = Path.Combine(_basePath, folder);
            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }

            // Full file path
            var filePath = Path.Combine(folderPath, uniqueFileName);
            var relativePath = Path.Combine(folder, uniqueFileName).Replace("\\", "/");

            // Save file
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            _logger.LogInformation("File uploaded successfully: {FilePath}", relativePath);

            return new UploadResultDto
            {
                FileName = uniqueFileName,
                FilePath = relativePath,
                FileUrl = $"/uploads/{relativePath}",
                FileSize = file.Length,
                ContentType = file.ContentType,
                UploadedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload file: {FileName}", file?.FileName);
            throw;
        }
    }

    public async Task<byte[]> DownloadFileAsync(string filePath)
    {
        try
        {
            var fullPath = Path.Combine(_basePath, filePath);
            
            if (!File.Exists(fullPath))
            {
                throw new FileNotFoundException($"File not found: {filePath}");
            }

            return await File.ReadAllBytesAsync(fullPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to download file: {FilePath}", filePath);
            throw;
        }
    }

    public async Task<string> GetFileUrlAsync(string filePath)
    {
        await Task.CompletedTask; // For async consistency
        return $"/uploads/{filePath}";
    }

    public async Task DeleteFileAsync(string filePath)
    {
        try
        {
            var fullPath = Path.Combine(_basePath, filePath);
            
            if (File.Exists(fullPath))
            {
                File.Delete(fullPath);
                _logger.LogInformation("File deleted successfully: {FilePath}", filePath);
            }
            
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete file: {FilePath}", filePath);
            throw;
        }
    }

    public async Task<bool> FileExistsAsync(string filePath)
    {
        await Task.CompletedTask; // For async consistency
        var fullPath = Path.Combine(_basePath, filePath);
        return File.Exists(fullPath);
    }

    public async Task<long> GetFileSizeAsync(string filePath)
    {
        try
        {
            var fullPath = Path.Combine(_basePath, filePath);
            
            if (!File.Exists(fullPath))
            {
                throw new FileNotFoundException($"File not found: {filePath}");
            }

            var fileInfo = new FileInfo(fullPath);
            await Task.CompletedTask; // For async consistency
            return fileInfo.Length;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get file size: {FilePath}", filePath);
            throw;
        }
    }

    public async Task<IEnumerable<string>> GetAllowedExtensionsAsync()
    {
        await Task.CompletedTask; // For async consistency
        return _allowedExtensions;
    }

    public async Task<long> GetMaxFileSizeAsync()
    {
        await Task.CompletedTask; // For async consistency
        return _maxFileSize;
    }

    public bool IsValidFileType(string fileName)
    {
        if (string.IsNullOrEmpty(fileName))
            return false;

        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return _allowedExtensions.Contains(extension);
    }

    public bool IsValidFileSize(long fileSize)
    {
        return fileSize > 0 && fileSize <= _maxFileSize;
    }
}
