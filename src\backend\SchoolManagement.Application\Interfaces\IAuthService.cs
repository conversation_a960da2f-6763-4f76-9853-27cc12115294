using SchoolManagement.Application.DTOs.Auth;

namespace SchoolManagement.Application.Interfaces;

public interface IAuthService
{
    Task<LoginResponseDto> LoginAsync(LoginRequestDto request);
    Task<RegisterResponseDto> RegisterAsync(RegisterRequestDto request);
    Task<RefreshTokenResponseDto> RefreshTokenAsync(RefreshTokenRequestDto request);
    Task LogoutAsync(string userId);
    Task<UserDto> GetCurrentUserAsync(string userId);
    Task<UserDto> UpdateProfileAsync(string userId, UpdateProfileRequestDto request);
    Task ChangePasswordAsync(string userId, ChangePasswordRequestDto request);
    Task ForgotPasswordAsync(ForgotPasswordRequestDto request);
    Task ResetPasswordAsync(ResetPasswordRequestDto request);
    Task VerifyEmailAsync(VerifyEmailRequestDto request);
    Task ResendEmailVerificationAsync(string userId);
    Task<IEnumerable<string>> GetUserPermissionsAsync(string userId);
    Task<IEnumerable<string>> GetUserRolesAsync(string userId);
    Task<bool> HasPermissionAsync(string userId, string permission);
}
