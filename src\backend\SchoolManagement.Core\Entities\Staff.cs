using System.ComponentModel.DataAnnotations;

namespace SchoolManagement.Core.Entities;

/// <summary>
/// Represents a staff member (teacher, administrator, etc.)
/// </summary>
public class Staff : TenantEntity
{
    [Required]
    [MaxLength(50)]
    public string EmployeeNumber { get; set; } = string.Empty;
    
    [Required]
    public Guid UserId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string LastName { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? MiddleName { get; set; }
    
    [MaxLength(100)]
    public string? ArabicFirstName { get; set; }
    
    [MaxLength(100)]
    public string? ArabicLastName { get; set; }
    
    [MaxLength(100)]
    public string? ArabicMiddleName { get; set; }
    
    [Required]
    public DateTime DateOfBirth { get; set; }
    
    [Required]
    [MaxLength(10)]
    public string Gender { get; set; } = string.Empty;
    
    [MaxLength(50)]
    public string? Nationality { get; set; }
    
    [MaxLength(50)]
    public string? NationalId { get; set; }
    
    [MaxLength(50)]
    public string? PassportNumber { get; set; }
    
    [MaxLength(100)]
    public string? PlaceOfBirth { get; set; }
    
    [MaxLength(20)]
    public string? EmergencyContactPhone { get; set; }
    
    [MaxLength(100)]
    public string? EmergencyContactName { get; set; }
    
    [MaxLength(100)]
    public string? EmergencyContactRelation { get; set; }
    
    [MaxLength(500)]
    public string? Address { get; set; }
    
    [MaxLength(100)]
    public string? City { get; set; }
    
    [MaxLength(100)]
    public string? State { get; set; }
    
    [MaxLength(20)]
    public string? PostalCode { get; set; }
    
    [MaxLength(100)]
    public string? Country { get; set; }
    
    public DateTime HireDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? TerminationDate { get; set; }
    
    public StaffStatus Status { get; set; } = StaffStatus.Active;
    
    [Required]
    [MaxLength(100)]
    public string Position { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? Department { get; set; }
    
    [MaxLength(100)]
    public string? Specialization { get; set; }
    
    public decimal Salary { get; set; }
    
    [MaxLength(20)]
    public string? SalaryCurrency { get; set; } = "USD";
    
    [MaxLength(50)]
    public string? PayrollFrequency { get; set; } = "Monthly";
    
    [MaxLength(100)]
    public string? BankAccount { get; set; }
    
    [MaxLength(100)]
    public string? BankName { get; set; }
    
    [MaxLength(50)]
    public string? BankBranch { get; set; }
    
    /// <summary>
    /// Years of experience
    /// </summary>
    public int ExperienceYears { get; set; } = 0;
    
    /// <summary>
    /// Education qualifications stored as JSON
    /// </summary>
    public string? Qualifications { get; set; }
    
    /// <summary>
    /// Certifications stored as JSON
    /// </summary>
    public string? Certifications { get; set; }
    
    /// <summary>
    /// Staff profile picture URL
    /// </summary>
    public string? ProfilePictureUrl { get; set; }
    
    /// <summary>
    /// Medical information stored as JSON
    /// </summary>
    public string? MedicalInfo { get; set; }
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    // Navigation properties
    public virtual User User { get; set; } = null!;
    public virtual ICollection<ClassSchedule> ClassSchedules { get; set; } = new List<ClassSchedule>();
    public virtual ICollection<StaffDocument> Documents { get; set; } = new List<StaffDocument>();
    public virtual ICollection<StaffLeave> Leaves { get; set; } = new List<StaffLeave>();
    public virtual ICollection<StaffAttendance> Attendances { get; set; } = new List<StaffAttendance>();
    public virtual ICollection<Payroll> Payrolls { get; set; } = new List<Payroll>();
    
    // Computed properties
    public string FullName => $"{FirstName} {MiddleName} {LastName}".Replace("  ", " ").Trim();
    public string DisplayName => $"{FirstName} {LastName}";
    public string ArabicFullName => $"{ArabicFirstName} {ArabicMiddleName} {ArabicLastName}".Replace("  ", " ").Trim();
    public int Age => DateTime.UtcNow.Year - DateOfBirth.Year - (DateTime.UtcNow.DayOfYear < DateOfBirth.DayOfYear ? 1 : 0);
}

/// <summary>
/// Staff status enumeration
/// </summary>
public enum StaffStatus
{
    Active,
    Inactive,
    OnLeave,
    Terminated,
    Suspended,
    Retired
}

/// <summary>
/// Staff documents (contracts, certificates, etc.)
/// </summary>
public class StaffDocument : TenantEntity
{
    [Required]
    public Guid StaffId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string DocumentType { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(200)]
    public string FileName { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(500)]
    public string FilePath { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? ContentType { get; set; }
    
    public long FileSize { get; set; }
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    public DateTime? ExpiryDate { get; set; }
    
    public bool IsRequired { get; set; } = false;
    
    public bool IsVerified { get; set; } = false;
    
    public DateTime? VerifiedAt { get; set; }
    
    public string? VerifiedBy { get; set; }
    
    // Navigation properties
    public virtual Staff Staff { get; set; } = null!;
}

/// <summary>
/// Staff leave requests and approvals
/// </summary>
public class StaffLeave : TenantEntity
{
    [Required]
    public Guid StaffId { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string LeaveType { get; set; } = string.Empty; // Annual, Sick, Maternity, etc.
    
    [Required]
    public DateTime StartDate { get; set; }
    
    [Required]
    public DateTime EndDate { get; set; }
    
    public int TotalDays { get; set; }
    
    [MaxLength(500)]
    public string? Reason { get; set; }
    
    [MaxLength(500)]
    public string? Comments { get; set; }
    
    public LeaveStatus Status { get; set; } = LeaveStatus.Pending;
    
    public DateTime? ApprovedAt { get; set; }
    
    public string? ApprovedBy { get; set; }
    
    [MaxLength(500)]
    public string? ApprovalComments { get; set; }
    
    // Navigation properties
    public virtual Staff Staff { get; set; } = null!;
}

/// <summary>
/// Leave status enumeration
/// </summary>
public enum LeaveStatus
{
    Pending,
    Approved,
    Rejected,
    Cancelled
}

/// <summary>
/// Staff attendance tracking
/// </summary>
public class StaffAttendance : TenantEntity
{
    [Required]
    public Guid StaffId { get; set; }
    
    [Required]
    public DateTime Date { get; set; }
    
    public TimeSpan? CheckInTime { get; set; }
    
    public TimeSpan? CheckOutTime { get; set; }
    
    public TimeSpan? BreakStartTime { get; set; }
    
    public TimeSpan? BreakEndTime { get; set; }
    
    public TimeSpan? TotalWorkingHours { get; set; }
    
    public TimeSpan? TotalBreakTime { get; set; }
    
    public AttendanceStatus Status { get; set; } = AttendanceStatus.Present;
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    public bool IsLate { get; set; } = false;
    
    public bool IsEarlyLeave { get; set; } = false;
    
    public TimeSpan? LateBy { get; set; }
    
    public TimeSpan? EarlyLeaveBy { get; set; }
    
    // Navigation properties
    public virtual Staff Staff { get; set; } = null!;
}

/// <summary>
/// Attendance status enumeration
/// </summary>
public enum AttendanceStatus
{
    Present,
    Absent,
    Late,
    HalfDay,
    OnLeave,
    Holiday
}

/// <summary>
/// Payroll records
/// </summary>
public class Payroll : TenantEntity
{
    [Required]
    public Guid StaffId { get; set; }
    
    [Required]
    public DateTime PayPeriodStart { get; set; }
    
    [Required]
    public DateTime PayPeriodEnd { get; set; }
    
    [Required]
    public decimal BasicSalary { get; set; }
    
    public decimal Allowances { get; set; } = 0;
    
    public decimal Overtime { get; set; } = 0;
    
    public decimal Bonuses { get; set; } = 0;
    
    public decimal Deductions { get; set; } = 0;
    
    public decimal Tax { get; set; } = 0;
    
    public decimal NetSalary { get; set; }
    
    [MaxLength(20)]
    public string Currency { get; set; } = "USD";
    
    public PayrollStatus Status { get; set; } = PayrollStatus.Draft;
    
    public DateTime? ProcessedAt { get; set; }
    
    public string? ProcessedBy { get; set; }
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    /// <summary>
    /// Detailed payroll breakdown stored as JSON
    /// </summary>
    public string? PayrollDetails { get; set; }
    
    // Navigation properties
    public virtual Staff Staff { get; set; } = null!;
}

/// <summary>
/// Payroll status enumeration
/// </summary>
public enum PayrollStatus
{
    Draft,
    Approved,
    Processed,
    Paid,
    Cancelled
}
