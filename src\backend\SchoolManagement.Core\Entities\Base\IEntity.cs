namespace SchoolManagement.Core.Entities.Base;

public interface IEntity
{
    Guid Id { get; set; }
    DateTime CreatedAt { get; set; }
    DateTime? UpdatedAt { get; set; }
}

public interface IAuditableEntity : IEntity
{
    string? CreatedBy { get; set; }
    string? UpdatedBy { get; set; }
}

public interface ISoftDelete
{
    bool IsDeleted { get; set; }
    DateTime? DeletedAt { get; set; }
    string? DeletedBy { get; set; }
}

public interface ITenantEntity
{
    Guid TenantId { get; set; }
}
