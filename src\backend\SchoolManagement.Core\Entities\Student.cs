using System.ComponentModel.DataAnnotations;

namespace SchoolManagement.Core.Entities;

/// <summary>
/// Represents a student in the school management system
/// </summary>
public class Student : TenantEntity
{
    [Required]
    [MaxLength(50)]
    public string StudentNumber { get; set; } = string.Empty;
    
    [Required]
    public Guid UserId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string LastName { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? MiddleName { get; set; }
    
    [MaxLength(100)]
    public string? ArabicFirstName { get; set; }
    
    [MaxLength(100)]
    public string? ArabicLastName { get; set; }
    
    [MaxLength(100)]
    public string? ArabicMiddleName { get; set; }
    
    [Required]
    public DateTime DateOfBirth { get; set; }
    
    [Required]
    [MaxLength(10)]
    public string Gender { get; set; } = string.Empty;
    
    [MaxLength(50)]
    public string? Nationality { get; set; }
    
    [MaxLength(50)]
    public string? NationalId { get; set; }
    
    [MaxLength(50)]
    public string? PassportNumber { get; set; }
    
    [MaxLength(100)]
    public string? PlaceOfBirth { get; set; }
    
    [MaxLength(50)]
    public string? BloodType { get; set; }
    
    [MaxLength(20)]
    public string? EmergencyContactPhone { get; set; }
    
    [MaxLength(100)]
    public string? EmergencyContactName { get; set; }
    
    [MaxLength(100)]
    public string? EmergencyContactRelation { get; set; }
    
    [MaxLength(500)]
    public string? Address { get; set; }
    
    [MaxLength(100)]
    public string? City { get; set; }
    
    [MaxLength(100)]
    public string? State { get; set; }
    
    [MaxLength(20)]
    public string? PostalCode { get; set; }
    
    [MaxLength(100)]
    public string? Country { get; set; }
    
    public DateTime EnrollmentDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? GraduationDate { get; set; }
    
    public StudentStatus Status { get; set; } = StudentStatus.Active;
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    /// <summary>
    /// Student profile picture URL
    /// </summary>
    public string? ProfilePictureUrl { get; set; }
    
    /// <summary>
    /// Medical information stored as JSON
    /// </summary>
    public string? MedicalInfo { get; set; }
    
    /// <summary>
    /// Special needs or accommodations
    /// </summary>
    public string? SpecialNeeds { get; set; }
    
    /// <summary>
    /// Transportation information
    /// </summary>
    public string? TransportationInfo { get; set; }
    
    // Navigation properties
    public virtual User User { get; set; } = null!;
    public virtual ICollection<StudentGuardian> StudentGuardians { get; set; } = new List<StudentGuardian>();
    public virtual ICollection<StudentEnrollment> Enrollments { get; set; } = new List<StudentEnrollment>();
    public virtual ICollection<StudentAttendance> Attendances { get; set; } = new List<StudentAttendance>();
    public virtual ICollection<StudentGrade> Grades { get; set; } = new List<StudentGrade>();
    public virtual ICollection<StudentFee> Fees { get; set; } = new List<StudentFee>();
    public virtual ICollection<StudentDocument> Documents { get; set; } = new List<StudentDocument>();
    
    // Computed properties
    public string FullName => $"{FirstName} {MiddleName} {LastName}".Replace("  ", " ").Trim();
    public string DisplayName => $"{FirstName} {LastName}";
    public string ArabicFullName => $"{ArabicFirstName} {ArabicMiddleName} {ArabicLastName}".Replace("  ", " ").Trim();
    public int Age => DateTime.UtcNow.Year - DateOfBirth.Year - (DateTime.UtcNow.DayOfYear < DateOfBirth.DayOfYear ? 1 : 0);
}

/// <summary>
/// Student status enumeration
/// </summary>
public enum StudentStatus
{
    Active,
    Inactive,
    Graduated,
    Transferred,
    Suspended,
    Expelled,
    Withdrawn
}

/// <summary>
/// Represents a guardian/parent of a student
/// </summary>
public class Guardian : TenantEntity
{
    [Required]
    [MaxLength(100)]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string LastName { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? MiddleName { get; set; }
    
    [MaxLength(256)]
    public string? Email { get; set; }
    
    [MaxLength(20)]
    public string? PhoneNumber { get; set; }
    
    [MaxLength(20)]
    public string? WorkPhone { get; set; }
    
    [MaxLength(100)]
    public string? Occupation { get; set; }
    
    [MaxLength(100)]
    public string? Employer { get; set; }
    
    [MaxLength(500)]
    public string? Address { get; set; }
    
    [MaxLength(100)]
    public string? City { get; set; }
    
    [MaxLength(100)]
    public string? State { get; set; }
    
    [MaxLength(20)]
    public string? PostalCode { get; set; }
    
    [MaxLength(100)]
    public string? Country { get; set; }
    
    [MaxLength(50)]
    public string? NationalId { get; set; }
    
    public DateTime? DateOfBirth { get; set; }
    
    [MaxLength(10)]
    public string? Gender { get; set; }
    
    public bool IsEmergencyContact { get; set; } = false;
    
    public bool CanPickupStudent { get; set; } = true;
    
    public bool ReceiveNotifications { get; set; } = true;
    
    // Navigation properties
    public virtual ICollection<StudentGuardian> StudentGuardians { get; set; } = new List<StudentGuardian>();
    
    // Computed properties
    public string FullName => $"{FirstName} {MiddleName} {LastName}".Replace("  ", " ").Trim();
    public string DisplayName => $"{FirstName} {LastName}";
}

/// <summary>
/// Many-to-many relationship between students and guardians
/// </summary>
public class StudentGuardian : TenantEntity
{
    [Required]
    public Guid StudentId { get; set; }
    
    [Required]
    public Guid GuardianId { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string Relationship { get; set; } = string.Empty; // Father, Mother, Guardian, etc.
    
    public bool IsPrimaryContact { get; set; } = false;
    
    public bool IsEmergencyContact { get; set; } = false;
    
    public bool CanPickupStudent { get; set; } = true;
    
    public bool ReceiveReports { get; set; } = true;
    
    public bool ReceiveNotifications { get; set; } = true;
    
    // Navigation properties
    public virtual Student Student { get; set; } = null!;
    public virtual Guardian Guardian { get; set; } = null!;
}

/// <summary>
/// Student documents (certificates, medical records, etc.)
/// </summary>
public class StudentDocument : TenantEntity
{
    [Required]
    public Guid StudentId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string DocumentType { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(200)]
    public string FileName { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(500)]
    public string FilePath { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? ContentType { get; set; }
    
    public long FileSize { get; set; }
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    public DateTime? ExpiryDate { get; set; }
    
    public bool IsRequired { get; set; } = false;
    
    public bool IsVerified { get; set; } = false;
    
    public DateTime? VerifiedAt { get; set; }
    
    public string? VerifiedBy { get; set; }
    
    // Navigation properties
    public virtual Student Student { get; set; } = null!;
}
