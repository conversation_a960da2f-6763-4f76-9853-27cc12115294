import { apiHelpers } from './apiClient';
import { AcademicYear, Term, Grade, Subject, ClassSchedule, StudentGrade, Exam } from '../../store/slices/academicSlice';

export const academicApi = {
  // Academic Years
  getAcademicYears: async (): Promise<AcademicYear[]> => {
    return apiHelpers.get('/academics/academic-years');
  },

  getCurrentAcademicYear: async (): Promise<AcademicYear> => {
    return apiHelpers.get('/academics/academic-years/current');
  },

  createAcademicYear: async (data: {
    name: string;
    startDate: string;
    endDate: string;
    description?: string;
  }): Promise<AcademicYear> => {
    return apiHelpers.post('/academics/academic-years', data);
  },

  updateAcademicYear: async (id: string, data: Partial<AcademicYear>): Promise<AcademicYear> => {
    return apiHelpers.put(`/academics/academic-years/${id}`, data);
  },

  deleteAcademicYear: async (id: string): Promise<void> => {
    return apiHelpers.delete(`/academics/academic-years/${id}`);
  },

  setCurrentAcademicYear: async (id: string): Promise<void> => {
    return apiHelpers.post(`/academics/academic-years/${id}/set-current`);
  },

  // Terms
  getTerms: async (academicYearId?: string): Promise<Term[]> => {
    return apiHelpers.get('/academics/terms', { params: { academicYearId } });
  },

  createTerm: async (data: {
    academicYearId: string;
    name: string;
    startDate: string;
    endDate: string;
    description?: string;
  }): Promise<Term> => {
    return apiHelpers.post('/academics/terms', data);
  },

  updateTerm: async (id: string, data: Partial<Term>): Promise<Term> => {
    return apiHelpers.put(`/academics/terms/${id}`, data);
  },

  deleteTerm: async (id: string): Promise<void> => {
    return apiHelpers.delete(`/academics/terms/${id}`);
  },

  setCurrentTerm: async (id: string): Promise<void> => {
    return apiHelpers.post(`/academics/terms/${id}/set-current`);
  },

  // Grades
  getGrades: async (): Promise<Grade[]> => {
    return apiHelpers.get('/academics/grades');
  },

  getGradeById: async (id: string): Promise<Grade> => {
    return apiHelpers.get(`/academics/grades/${id}`);
  },

  createGrade: async (data: {
    name: string;
    level: number;
    section?: string;
    description?: string;
    maxStudents?: number;
  }): Promise<Grade> => {
    return apiHelpers.post('/academics/grades', data);
  },

  updateGrade: async (id: string, data: Partial<Grade>): Promise<Grade> => {
    return apiHelpers.put(`/academics/grades/${id}`, data);
  },

  deleteGrade: async (id: string): Promise<void> => {
    return apiHelpers.delete(`/academics/grades/${id}`);
  },

  // Subjects
  getSubjects: async (): Promise<Subject[]> => {
    return apiHelpers.get('/academics/subjects');
  },

  getSubjectById: async (id: string): Promise<Subject> => {
    return apiHelpers.get(`/academics/subjects/${id}`);
  },

  createSubject: async (data: {
    name: string;
    arabicName?: string;
    code: string;
    description?: string;
    creditHours?: number;
    isCore?: boolean;
    department?: string;
  }): Promise<Subject> => {
    return apiHelpers.post('/academics/subjects', data);
  },

  updateSubject: async (id: string, data: Partial<Subject>): Promise<Subject> => {
    return apiHelpers.put(`/academics/subjects/${id}`, data);
  },

  deleteSubject: async (id: string): Promise<void> => {
    return apiHelpers.delete(`/academics/subjects/${id}`);
  },

  // Grade-Subject Relationships
  assignSubjectToGrade: async (gradeId: string, subjectId: string, data: {
    weeklyHours?: number;
    isRequired?: boolean;
    passingGrade?: number;
    maxGrade?: number;
  }): Promise<void> => {
    return apiHelpers.post(`/academics/grades/${gradeId}/subjects/${subjectId}`, data);
  },

  removeSubjectFromGrade: async (gradeId: string, subjectId: string): Promise<void> => {
    return apiHelpers.delete(`/academics/grades/${gradeId}/subjects/${subjectId}`);
  },

  getGradeSubjects: async (gradeId: string): Promise<Subject[]> => {
    return apiHelpers.get(`/academics/grades/${gradeId}/subjects`);
  },

  // Class Schedules
  getClassSchedules: async (gradeId?: string): Promise<ClassSchedule[]> => {
    return apiHelpers.get('/academics/schedules', { params: { gradeId } });
  },

  getClassScheduleById: async (id: string): Promise<ClassSchedule> => {
    return apiHelpers.get(`/academics/schedules/${id}`);
  },

  createClassSchedule: async (data: {
    gradeId: string;
    subjectId: string;
    academicYearId: string;
    staffId?: string;
    dayOfWeek: string;
    startTime: string;
    endTime: string;
    classroom?: string;
    notes?: string;
  }): Promise<ClassSchedule> => {
    return apiHelpers.post('/academics/schedules', data);
  },

  updateClassSchedule: async (id: string, data: Partial<ClassSchedule>): Promise<ClassSchedule> => {
    return apiHelpers.put(`/academics/schedules/${id}`, data);
  },

  deleteClassSchedule: async (id: string): Promise<void> => {
    return apiHelpers.delete(`/academics/schedules/${id}`);
  },

  getWeeklySchedule: async (gradeId: string, academicYearId: string): Promise<{
    [dayOfWeek: string]: ClassSchedule[];
  }> => {
    return apiHelpers.get(`/academics/schedules/weekly`, {
      params: { gradeId, academicYearId }
    });
  },

  // Student Grades
  getStudentGrades: async (studentId: string, termId?: string): Promise<StudentGrade[]> => {
    return apiHelpers.get(`/academics/student-grades`, {
      params: { studentId, termId }
    });
  },

  getGradesBySubject: async (subjectId: string, termId: string, gradeId?: string): Promise<StudentGrade[]> => {
    return apiHelpers.get(`/academics/grades-by-subject`, {
      params: { subjectId, termId, gradeId }
    });
  },

  recordStudentGrade: async (data: {
    studentId: string;
    subjectId: string;
    termId: string;
    examId?: string;
    gradeType: string;
    score: number;
    maxScore?: number;
    comments?: string;
  }): Promise<StudentGrade> => {
    return apiHelpers.post('/academics/student-grades', data);
  },

  updateStudentGrade: async (id: string, data: Partial<StudentGrade>): Promise<StudentGrade> => {
    return apiHelpers.put(`/academics/student-grades/${id}`, data);
  },

  deleteStudentGrade: async (id: string): Promise<void> => {
    return apiHelpers.delete(`/academics/student-grades/${id}`);
  },

  bulkRecordGrades: async (grades: Array<{
    studentId: string;
    subjectId: string;
    termId: string;
    examId?: string;
    gradeType: string;
    score: number;
    maxScore?: number;
    comments?: string;
  }>): Promise<{
    recordedCount: number;
    errors?: Array<{
      studentId: string;
      message: string;
    }>;
  }> => {
    return apiHelpers.post('/academics/student-grades/bulk', { grades });
  },

  // Exams
  getExams: async (termId?: string, subjectId?: string): Promise<Exam[]> => {
    return apiHelpers.get('/academics/exams', { params: { termId, subjectId } });
  },

  getExamById: async (id: string): Promise<Exam> => {
    return apiHelpers.get(`/academics/exams/${id}`);
  },

  createExam: async (data: {
    subjectId: string;
    termId: string;
    name: string;
    type: string;
    description?: string;
    examDate: string;
    duration: string;
    maxScore?: number;
    passingScore?: number;
    classroom?: string;
    instructions?: string;
  }): Promise<Exam> => {
    return apiHelpers.post('/academics/exams', data);
  },

  updateExam: async (id: string, data: Partial<Exam>): Promise<Exam> => {
    return apiHelpers.put(`/academics/exams/${id}`, data);
  },

  deleteExam: async (id: string): Promise<void> => {
    return apiHelpers.delete(`/academics/exams/${id}`);
  },

  getExamResults: async (examId: string): Promise<Array<{
    studentId: string;
    studentName: string;
    score: number;
    percentage: number;
    letterGrade: string;
    rank: number;
  }>> => {
    return apiHelpers.get(`/academics/exams/${examId}/results`);
  },

  // Academic Reports
  getStudentTranscript: async (studentId: string, academicYearId?: string): Promise<{
    student: {
      id: string;
      name: string;
      studentNumber: string;
      grade: string;
    };
    academicYear: string;
    terms: Array<{
      termName: string;
      subjects: Array<{
        subjectName: string;
        grades: Array<{
          type: string;
          score: number;
          maxScore: number;
          percentage: number;
        }>;
        finalGrade: number;
        letterGrade: string;
        credits: number;
      }>;
      gpa: number;
    }>;
    overallGPA: number;
    totalCredits: number;
  }> => {
    return apiHelpers.get(`/academics/transcripts/${studentId}`, {
      params: { academicYearId }
    });
  },

  getClassReport: async (gradeId: string, subjectId: string, termId: string): Promise<{
    grade: string;
    subject: string;
    term: string;
    totalStudents: number;
    averageScore: number;
    highestScore: number;
    lowestScore: number;
    passRate: number;
    gradeDistribution: Array<{
      letterGrade: string;
      count: number;
      percentage: number;
    }>;
    students: Array<{
      studentId: string;
      studentName: string;
      finalScore: number;
      letterGrade: string;
      rank: number;
    }>;
  }> => {
    return apiHelpers.get('/academics/class-report', {
      params: { gradeId, subjectId, termId }
    });
  },

  generateReportCard: async (studentId: string, termId: string): Promise<void> => {
    return apiHelpers.download(`/academics/report-card/${studentId}/${termId}`, `report-card-${studentId}.pdf`);
  },

  // Academic Analytics
  getAcademicAnalytics: async (academicYearId?: string): Promise<{
    totalStudents: number;
    totalSubjects: number;
    totalExams: number;
    averageGPA: number;
    passRate: number;
    gradeDistribution: Array<{
      grade: string;
      count: number;
      percentage: number;
    }>;
    subjectPerformance: Array<{
      subjectName: string;
      averageScore: number;
      passRate: number;
      totalStudents: number;
    }>;
    monthlyTrends: Array<{
      month: string;
      averageScore: number;
      examCount: number;
    }>;
  }> => {
    return apiHelpers.get('/academics/analytics', { params: { academicYearId } });
  },

  // Curriculum Management
  getCurriculum: async (gradeId: string): Promise<{
    grade: string;
    subjects: Array<{
      subjectId: string;
      subjectName: string;
      weeklyHours: number;
      isRequired: boolean;
      syllabus?: string;
      learningObjectives?: string[];
      assessmentMethods?: string[];
    }>;
    totalWeeklyHours: number;
  }> => {
    return apiHelpers.get(`/academics/curriculum/${gradeId}`);
  },

  updateCurriculum: async (gradeId: string, curriculum: {
    subjects: Array<{
      subjectId: string;
      weeklyHours: number;
      isRequired: boolean;
      syllabus?: string;
      learningObjectives?: string[];
      assessmentMethods?: string[];
    }>;
  }): Promise<void> => {
    return apiHelpers.put(`/academics/curriculum/${gradeId}`, curriculum);
  },
};
