import { apiHelpers, PaginatedResponse } from './apiClient';
import { Tenant, TenantBranding } from '../../store/slices/tenantSlice';

export interface CreateTenantRequest {
  name: string;
  code: string;
  description?: string;
  contactEmail: string;
  contactPhone?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  subscriptionStartDate?: string;
  subscriptionEndDate?: string;
  subscriptionPlan?: string;
  maxStudents?: number;
  maxStaff?: number;
  settings?: Record<string, any>;
  brandingConfig?: Record<string, any>;
  complianceConfig?: Record<string, any>;
}

export interface UpdateTenantRequest {
  id: string;
  name: string;
  description?: string;
  contactEmail: string;
  contactPhone?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  isActive: boolean;
  subscriptionStartDate?: string;
  subscriptionEndDate?: string;
  subscriptionPlan?: string;
  maxStudents?: number;
  maxStaff?: number;
  settings?: Record<string, any>;
  brandingConfig?: Record<string, any>;
  complianceConfig?: Record<string, any>;
}

export interface TenantConfigurationRequest {
  tenantId: string;
  settings?: Record<string, any>;
  brandingConfig?: Record<string, any>;
  complianceConfig?: Record<string, any>;
}

export interface TenantStatusResponse {
  tenantId: string;
  isActive: boolean;
  name: string;
  code: string;
  subscriptionPlan?: string;
  subscriptionEndDate?: string;
}

export const tenantApi = {
  // Get all tenants (System Admin only)
  getAllTenants: async (params?: {
    page?: number;
    pageSize?: number;
    search?: string;
    isActive?: boolean;
  }): Promise<PaginatedResponse<Tenant>> => {
    return apiHelpers.get('/tenants', { params });
  },

  // Get tenant by ID
  getTenantById: async (id: string): Promise<Tenant> => {
    return apiHelpers.get(`/tenants/${id}`);
  },

  // Get tenant by code
  getTenantByCode: async (code: string): Promise<Tenant> => {
    return apiHelpers.get(`/tenants/by-code/${code}`);
  },

  // Create new tenant (System Admin only)
  createTenant: async (tenantData: CreateTenantRequest): Promise<Tenant> => {
    return apiHelpers.post('/tenants', tenantData);
  },

  // Update tenant
  updateTenant: async (id: string, tenantData: UpdateTenantRequest): Promise<Tenant> => {
    return apiHelpers.put(`/tenants/${id}`, tenantData);
  },

  // Delete tenant (System Admin only)
  deleteTenant: async (id: string): Promise<void> => {
    return apiHelpers.delete(`/tenants/${id}`);
  },

  // Get tenant status
  getTenantStatus: async (id: string): Promise<TenantStatusResponse> => {
    return apiHelpers.get(`/tenants/${id}/status`);
  },

  // Update tenant configuration
  updateTenantConfiguration: async (
    id: string,
    configuration: TenantConfigurationRequest
  ): Promise<TenantConfigurationRequest> => {
    return apiHelpers.put(`/tenants/${id}/configuration`, configuration);
  },

  // Update tenant branding
  updateTenantBranding: async (id: string, branding: TenantBranding): Promise<TenantBranding> => {
    return apiHelpers.put(`/tenants/${id}/branding`, branding);
  },

  // Upload tenant logo
  uploadTenantLogo: async (id: string, file: File): Promise<{ logoUrl: string }> => {
    return apiHelpers.upload(`/tenants/${id}/logo`, file);
  },

  // Get tenant statistics
  getTenantStatistics: async (id: string): Promise<{
    totalStudents: number;
    totalStaff: number;
    totalRevenue: number;
    pendingPayments: number;
    attendanceRate: number;
    activeUsers: number;
    storageUsed: number;
    lastActivity: string;
  }> => {
    return apiHelpers.get(`/tenants/${id}/statistics`);
  },

  // Get tenant usage metrics
  getTenantUsage: async (id: string, period?: string): Promise<{
    period: string;
    apiCalls: number;
    storageUsed: number;
    bandwidthUsed: number;
    activeUsers: number;
    peakConcurrentUsers: number;
    uptime: number;
  }> => {
    return apiHelpers.get(`/tenants/${id}/usage`, { params: { period } });
  },

  // Get tenant billing information
  getTenantBilling: async (id: string): Promise<{
    subscriptionPlan: string;
    billingCycle: string;
    nextBillingDate: string;
    amount: number;
    currency: string;
    paymentMethod: string;
    invoices: Array<{
      id: string;
      invoiceNumber: string;
      amount: number;
      status: string;
      dueDate: string;
      paidDate?: string;
    }>;
  }> => {
    return apiHelpers.get(`/tenants/${id}/billing`);
  },

  // Update tenant subscription
  updateTenantSubscription: async (id: string, subscription: {
    plan: string;
    billingCycle: string;
    maxStudents?: number;
    maxStaff?: number;
    features?: string[];
  }): Promise<{
    message: string;
    effectiveDate: string;
    prorationAmount?: number;
  }> => {
    return apiHelpers.put(`/tenants/${id}/subscription`, subscription);
  },

  // Suspend tenant
  suspendTenant: async (id: string, reason: string): Promise<{ message: string }> => {
    return apiHelpers.post(`/tenants/${id}/suspend`, { reason });
  },

  // Reactivate tenant
  reactivateTenant: async (id: string): Promise<{ message: string }> => {
    return apiHelpers.post(`/tenants/${id}/reactivate`);
  },

  // Get tenant audit logs
  getTenantAuditLogs: async (id: string, params?: {
    page?: number;
    pageSize?: number;
    startDate?: string;
    endDate?: string;
    action?: string;
    userId?: string;
  }): Promise<PaginatedResponse<{
    id: string;
    action: string;
    entityName: string;
    entityId: string;
    oldValues?: any;
    newValues?: any;
    userId: string;
    userName: string;
    ipAddress: string;
    userAgent: string;
    timestamp: string;
  }>> => {
    return apiHelpers.get(`/tenants/${id}/audit-logs`, { params });
  },

  // Export tenant data
  exportTenantData: async (id: string, format: 'json' | 'csv' | 'excel' = 'json'): Promise<void> => {
    return apiHelpers.download(`/tenants/${id}/export?format=${format}`, `tenant-${id}-data.${format}`);
  },

  // Import tenant data
  importTenantData: async (id: string, file: File): Promise<{
    message: string;
    importedRecords: number;
    errors?: string[];
  }> => {
    return apiHelpers.upload(`/tenants/${id}/import`, file);
  },

  // Get tenant backup
  createTenantBackup: async (id: string): Promise<{
    backupId: string;
    downloadUrl: string;
    expiresAt: string;
  }> => {
    return apiHelpers.post(`/tenants/${id}/backup`);
  },

  // Restore tenant from backup
  restoreTenantBackup: async (id: string, backupId: string): Promise<{
    message: string;
    restoredAt: string;
  }> => {
    return apiHelpers.post(`/tenants/${id}/restore`, { backupId });
  },

  // Get tenant compliance report
  getTenantComplianceReport: async (id: string, reportType: string): Promise<{
    reportType: string;
    generatedAt: string;
    status: string;
    findings: Array<{
      category: string;
      severity: string;
      description: string;
      recommendation: string;
    }>;
    downloadUrl?: string;
  }> => {
    return apiHelpers.get(`/tenants/${id}/compliance/${reportType}`);
  },

  // Update tenant compliance settings
  updateTenantCompliance: async (id: string, compliance: {
    country: string;
    regulations: string[];
    dataRetentionPeriod: number;
    enableGDPR: boolean;
    enableFERPA: boolean;
    customSettings?: Record<string, any>;
  }): Promise<{ message: string }> => {
    return apiHelpers.put(`/tenants/${id}/compliance`, compliance);
  },
};
