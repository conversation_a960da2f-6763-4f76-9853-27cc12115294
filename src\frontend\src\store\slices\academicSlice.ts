import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { academicApi } from '../../services/api/academicApi';

export interface AcademicYear {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  isActive: boolean;
  isCurrent: boolean;
  description?: string;
  terms: Term[];
}

export interface Term {
  id: string;
  academicYearId: string;
  name: string;
  startDate: string;
  endDate: string;
  isActive: boolean;
  isCurrent: boolean;
  description?: string;
}

export interface Grade {
  id: string;
  name: string;
  level: number;
  section?: string;
  description?: string;
  maxStudents: number;
  isActive: boolean;
  currentStudents: number;
  subjects: Subject[];
}

export interface Subject {
  id: string;
  name: string;
  arabicName?: string;
  code: string;
  description?: string;
  creditHours: number;
  isActive: boolean;
  isCore: boolean;
  department?: string;
}

export interface ClassSchedule {
  id: string;
  gradeId: string;
  gradeName: string;
  subjectId: string;
  subjectName: string;
  academicYearId: string;
  staffId?: string;
  staffName?: string;
  dayOfWeek: string;
  startTime: string;
  endTime: string;
  classroom?: string;
  notes?: string;
  isActive: boolean;
}

export interface StudentGrade {
  id: string;
  studentId: string;
  studentName: string;
  subjectId: string;
  subjectName: string;
  termId: string;
  termName: string;
  examId?: string;
  gradeType: string;
  score: number;
  maxScore: number;
  percentage: number;
  letterGrade?: string;
  comments?: string;
  gradeDate: string;
  gradedBy?: string;
}

export interface Exam {
  id: string;
  subjectId: string;
  subjectName: string;
  termId: string;
  termName: string;
  name: string;
  type: string;
  description?: string;
  examDate: string;
  duration: string;
  maxScore: number;
  passingScore: number;
  classroom?: string;
  instructions?: string;
  isActive: boolean;
}

export interface AcademicState {
  academicYears: AcademicYear[];
  currentAcademicYear: AcademicYear | null;
  terms: Term[];
  currentTerm: Term | null;
  grades: Grade[];
  subjects: Subject[];
  schedules: ClassSchedule[];
  studentGrades: StudentGrade[];
  exams: Exam[];
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: string | null;
  lastUpdated: number | null;
}

const initialState: AcademicState = {
  academicYears: [],
  currentAcademicYear: null,
  terms: [],
  currentTerm: null,
  grades: [],
  subjects: [],
  schedules: [],
  studentGrades: [],
  exams: [],
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
  lastUpdated: null,
};

// Async thunks
export const fetchAcademicYears = createAsyncThunk(
  'academic/fetchAcademicYears',
  async (_, { rejectWithValue }) => {
    try {
      const response = await academicApi.getAcademicYears();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch academic years');
    }
  }
);

export const fetchCurrentAcademicYear = createAsyncThunk(
  'academic/fetchCurrentAcademicYear',
  async (_, { rejectWithValue }) => {
    try {
      const response = await academicApi.getCurrentAcademicYear();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch current academic year');
    }
  }
);

export const fetchGrades = createAsyncThunk(
  'academic/fetchGrades',
  async (_, { rejectWithValue }) => {
    try {
      const response = await academicApi.getGrades();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch grades');
    }
  }
);

export const fetchSubjects = createAsyncThunk(
  'academic/fetchSubjects',
  async (_, { rejectWithValue }) => {
    try {
      const response = await academicApi.getSubjects();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch subjects');
    }
  }
);

export const fetchClassSchedules = createAsyncThunk(
  'academic/fetchClassSchedules',
  async (gradeId?: string, { rejectWithValue }) => {
    try {
      const response = await academicApi.getClassSchedules(gradeId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch class schedules');
    }
  }
);

export const fetchStudentGrades = createAsyncThunk(
  'academic/fetchStudentGrades',
  async (params: { studentId: string; termId?: string }, { rejectWithValue }) => {
    try {
      const response = await academicApi.getStudentGrades(params.studentId, params.termId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch student grades');
    }
  }
);

export const fetchExams = createAsyncThunk(
  'academic/fetchExams',
  async (termId?: string, { rejectWithValue }) => {
    try {
      const response = await academicApi.getExams(termId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch exams');
    }
  }
);

export const createAcademicYear = createAsyncThunk(
  'academic/createAcademicYear',
  async (data: any, { rejectWithValue }) => {
    try {
      const response = await academicApi.createAcademicYear(data);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create academic year');
    }
  }
);

export const createGrade = createAsyncThunk(
  'academic/createGrade',
  async (data: any, { rejectWithValue }) => {
    try {
      const response = await academicApi.createGrade(data);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create grade');
    }
  }
);

export const createSubject = createAsyncThunk(
  'academic/createSubject',
  async (data: any, { rejectWithValue }) => {
    try {
      const response = await academicApi.createSubject(data);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create subject');
    }
  }
);

export const recordStudentGrade = createAsyncThunk(
  'academic/recordStudentGrade',
  async (data: any, { rejectWithValue }) => {
    try {
      const response = await academicApi.recordStudentGrade(data);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to record student grade');
    }
  }
);

const academicSlice = createSlice({
  name: 'academic',
  initialState,
  reducers: {
    setCurrentAcademicYear: (state, action: PayloadAction<AcademicYear | null>) => {
      state.currentAcademicYear = action.payload;
      state.terms = action.payload?.terms || [];
      state.currentTerm = action.payload?.terms?.find(t => t.isCurrent) || null;
    },
    setCurrentTerm: (state, action: PayloadAction<Term | null>) => {
      state.currentTerm = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetAcademicState: (state) => {
      state.error = null;
      state.studentGrades = [];
      state.schedules = [];
      state.exams = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Academic Years
      .addCase(fetchAcademicYears.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAcademicYears.fulfilled, (state, action) => {
        state.isLoading = false;
        state.academicYears = action.payload;
        state.lastUpdated = Date.now();
      })
      .addCase(fetchAcademicYears.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch Current Academic Year
      .addCase(fetchCurrentAcademicYear.fulfilled, (state, action) => {
        state.currentAcademicYear = action.payload;
        state.terms = action.payload?.terms || [];
        state.currentTerm = action.payload?.terms?.find(t => t.isCurrent) || null;
      })
      
      // Fetch Grades
      .addCase(fetchGrades.fulfilled, (state, action) => {
        state.grades = action.payload;
      })
      
      // Fetch Subjects
      .addCase(fetchSubjects.fulfilled, (state, action) => {
        state.subjects = action.payload;
      })
      
      // Fetch Class Schedules
      .addCase(fetchClassSchedules.fulfilled, (state, action) => {
        state.schedules = action.payload;
      })
      
      // Fetch Student Grades
      .addCase(fetchStudentGrades.fulfilled, (state, action) => {
        state.studentGrades = action.payload;
      })
      
      // Fetch Exams
      .addCase(fetchExams.fulfilled, (state, action) => {
        state.exams = action.payload;
      })
      
      // Create Academic Year
      .addCase(createAcademicYear.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createAcademicYear.fulfilled, (state, action) => {
        state.isCreating = false;
        state.academicYears.unshift(action.payload);
      })
      .addCase(createAcademicYear.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.payload as string;
      })
      
      // Create Grade
      .addCase(createGrade.fulfilled, (state, action) => {
        state.grades.push(action.payload);
      })
      
      // Create Subject
      .addCase(createSubject.fulfilled, (state, action) => {
        state.subjects.push(action.payload);
      })
      
      // Record Student Grade
      .addCase(recordStudentGrade.fulfilled, (state, action) => {
        state.studentGrades.push(action.payload);
      });
  },
});

export const {
  setCurrentAcademicYear,
  setCurrentTerm,
  clearError,
  resetAcademicState,
} = academicSlice.actions;

export default academicSlice.reducer;
