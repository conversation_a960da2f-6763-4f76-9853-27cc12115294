import React, { useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box, Typography } from '@mui/material';
import { useAppDispatch } from '../../store/store';
import { setPageTitle, setBreadcrumbs } from '../../store/slices/uiSlice';

const StaffListPage: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setPageTitle('Staff'));
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', path: '/dashboard' },
      { label: 'Staff' }
    ]));
  }, [dispatch]);

  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 700, mb: 3 }}>
        Staff Management
      </Typography>
      <Typography variant="body1" color="text.secondary">
        Staff management functionality will be implemented here.
      </Typography>
    </Box>
  );
};

const StaffPage: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<StaffListPage />} />
      <Route path="/teachers" element={<StaffListPage />} />
      <Route path="/payroll" element={<StaffListPage />} />
    </Routes>
  );
};

export default StaffPage;
